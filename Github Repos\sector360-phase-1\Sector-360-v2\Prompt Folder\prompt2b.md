# Sector360 Marketplace Demo - Part 2b: Supporting Models

## Additional Models

### Group Model
```php
// app/Models/Group.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use <PERSON><PERSON>\Sluggable\SlugOptions;

class Group extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'industry_id', 'name', 'slug', 'sort_order'
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function industry()
    {
        return $this->belongsTo(Industry::class);
    }

    public function categories()
    {
        return $this->hasMany(Category::class);
    }

    public function listings()
    {
        return $this->hasMany(Listing::class);
    }
}
```

### Category Model
```php
// app/Models/Category.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Category extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'industry_id', 'group_id', 'name', 'slug', 
        'description', 'sort_order', 'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function industry()
    {
        return $this->belongsTo(Industry::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function equipmentTypes()
    {
        return $this->hasMany(EquipmentType::class);
    }

    public function listings()
    {
        return $this->hasMany(Listing::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)->orderBy('sort_order');
    }
}
```

### EquipmentType Model
```php
// app/Models/EquipmentType.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class EquipmentType extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'category_id', 'name', 'slug'
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function listings()
    {
        return $this->hasMany(Listing::class);
    }
}
```

### Brand Model
```php
// app/Models/Brand.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Brand extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = ['name', 'slug', 'logo_url', 'is_featured'];

    protected $casts = [
        'is_featured' => 'boolean',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function listings()
    {
        return $this->hasMany(Listing::class);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function getListingCountAttribute()
    {
        return $this->listings()->active()->count();
    }
}
```

### ListingImage Model
```php
// app/Models/ListingImage.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ListingImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'listing_id', 'path', 'thumbnail_path', 'sort_order', 'is_primary'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'sort_order' => 'integer',
    ];

    public function listing()
    {
        return $this->belongsTo(Listing::class);
    }

    public function getFullPathAttribute()
    {
        return asset('storage/' . $this->path);
    }

    public function getThumbnailUrlAttribute()
    {
        return asset('storage/' . ($this->thumbnail_path ?? $this->path));
    }
}
```

### QuoteRequest Model
```php
// app/Models/QuoteRequest.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuoteRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'listing_id', 'user_id', 'name', 'email', 
        'phone', 'company', 'message', 'status'
    ];

    protected $casts = [
        'responded_at' => 'datetime',
    ];

    public function listing()
    {
        return $this->belongsTo(Listing::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeResponded($query)
    {
        return $query->where('status', 'responded');
    }

    public function markAsResponded()
    {
        $this->update([
            'status' => 'responded',
            'responded_at' => now(),
        ]);
    }

    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'pending' => '<span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Pending</span>',
            'responded' => '<span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Responded</span>',
            'closed' => '<span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Closed</span>',
            default => '<span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Unknown</span>',
        };
    }
}
```

### SavedSearch Model
```php
// app/Models/SavedSearch.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavedSearch extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'name', 'criteria', 'email_alerts', 'last_alerted_at'
    ];

    protected $casts = [
        'criteria' => 'array',
        'email_alerts' => 'boolean',
        'last_alerted_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getSearchUrlAttribute()
    {
        $params = http_build_query($this->criteria);
        return route('listings.search') . '?' . $params;
    }
}
```
