<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brands = [
            ['name' => 'Haas', 'is_featured' => true],
            ['name' => '<PERSON><PERSON>', 'is_featured' => true],
            ['name' => 'DMG Mori', 'is_featured' => true],
            ['name' => 'Okuma', 'is_featured' => true],
            ['name' => 'Fanuc', 'is_featured' => false],
            ['name' => 'Siemens', 'is_featured' => false],
            ['name' => 'Mitsubishi', 'is_featured' => false],
            ['name' => '<PERSON><PERSON>', 'is_featured' => false],
            ['name' => 'Hurco', 'is_featured' => false],
            ['name' => 'Brother', 'is_featured' => false],
            ['name' => 'Makino', 'is_featured' => false],
            ['name' => 'Trumpf', 'is_featured' => false],
            ['name' => 'Amada', 'is_featured' => false],
            ['name' => 'Cincinnati', 'is_featured' => false],
            ['name' => 'Bridgeport', 'is_featured' => false],
        ];

        foreach ($brands as $brand) {
            \App\Models\Brand::create($brand);
        }
    }
}
