<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- Supabase Configuration -->
        <meta name="supabase-url" content="{{ config('supabase.url') }}">
        <meta name="supabase-anon-key" content="{{ config('supabase.anon_key') }}">

        <title>@yield('title', 'Sector360 - The World\'s Business Marketplace')</title>
        <meta name="description" content="@yield('description', 'Find and sell industrial equipment on the world\'s leading business marketplace. CNC machines, metalworking equipment, and more.')">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        @stack('styles')
    </head>
    <body class="font-sans antialiased bg-sector-bg text-sector-text">
        <div class="min-h-screen">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-sector-card border-b border-sector-border">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Flash Messages -->
            @if(session('success'))
                <div id="flash-success" class="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
                    <div class="bg-green-900/90 border border-green-600 text-green-100 px-6 py-4 rounded-lg shadow-lg backdrop-blur-sm" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span class="font-medium">{{ session('success') }}</span>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div id="flash-error" class="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
                    <div class="bg-red-900/90 border border-red-600 text-red-100 px-6 py-4 rounded-lg shadow-lg backdrop-blur-sm" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-400 mr-3"></i>
                            <span class="font-medium">{{ session('error') }}</span>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('message'))
                <div id="flash-message" class="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
                    <div class="bg-blue-900/90 border border-blue-600 text-blue-100 px-6 py-4 rounded-lg shadow-lg backdrop-blur-sm" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                            <span class="font-medium">{{ session('message') }}</span>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Page Content -->
            <main>
                @yield('content')
            </main>

            @include('layouts.footer')
        </div>

        @stack('scripts')

        <!-- Flash Message Auto-Fade -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-fade flash messages after 1 second
            const flashMessages = document.querySelectorAll('#flash-success, #flash-error, #flash-message');

            flashMessages.forEach(function(message) {
                if (message) {
                    // Fade out after 1 second
                    setTimeout(function() {
                        message.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        message.style.opacity = '0';
                        message.style.transform = 'translate(-50%, -20px)';

                        // Remove from DOM after fade animation
                        setTimeout(function() {
                            message.remove();
                        }, 300);
                    }, 1000);

                    // Allow manual dismissal by clicking
                    message.addEventListener('click', function() {
                        message.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        message.style.opacity = '0';
                        message.style.transform = 'translate(-50%, -20px)';

                        setTimeout(function() {
                            message.remove();
                        }, 300);
                    });
                }
            });
        });
        </script>
    </body>
</html>
