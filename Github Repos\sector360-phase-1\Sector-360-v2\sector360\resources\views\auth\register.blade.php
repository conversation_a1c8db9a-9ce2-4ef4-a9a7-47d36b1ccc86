@extends('layouts.app')

@section('title', 'Join Sector360 - The World\'s Business Marketplace')

@section('content')
<div class="min-h-screen bg-sector-bg flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="flex items-center justify-center space-x-2 mb-6">
                <div class="w-10 h-10 bg-sector-primary rounded-lg flex items-center justify-center">
                    <i class="fas fa-industry text-white text-lg"></i>
                </div>
                <span class="text-2xl font-bold text-sector-text">Sector360</span>
            </div>
            <h2 class="text-3xl font-bold text-sector-text">Join the marketplace</h2>
            <p class="mt-2 text-sector-text-muted">Connect with buyers and sellers worldwide</p>
        </div>

        <!-- Google Sign In Button -->
        <div>
            <a href="#" class="w-full flex justify-center items-center px-4 py-3 border border-sector-border rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 transition-colors font-medium">
                <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
            </a>
        </div>

        <!-- Divider -->
        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-sector-border"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-sector-bg text-sector-text-muted">Or continue with email</span>
            </div>
        </div>

        <!-- Email Registration Form -->
        <form method="POST" action="{{ route('register') }}" class="space-y-6">
            @csrf

            <!-- Name and Email -->
            <div class="space-y-4">
                <div>
                    <input type="text"
                           name="name"
                           value="{{ old('name') }}"
                           required
                           placeholder="Full name"
                           class="w-full bg-sector-card border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                    @error('name')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <input type="email"
                           name="email"
                           value="{{ old('email') }}"
                           required
                           placeholder="Email address"
                           class="w-full bg-sector-card border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                    @error('email')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <input type="password"
                           name="password"
                           required
                           placeholder="Password"
                           class="w-full bg-sector-card border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                    @error('password')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <input type="password"
                           name="password_confirmation"
                           required
                           placeholder="Confirm password"
                           class="w-full bg-sector-card border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                </div>
            </div>

            <!-- Hidden fields with defaults - we'll collect this later -->
            <input type="hidden" name="user_type" value="buyer">
            <input type="hidden" name="company_name" value="Individual">

            <!-- Submit Button -->
            <button type="submit" class="w-full btn-primary py-3 text-lg font-medium">
                Create Account
            </button>
        </form>

        <!-- Sign In Link -->
        <div class="text-center">
            <p class="text-sector-text-muted">
                Already have an account?
                <a href="{{ route('login') }}" class="text-sector-primary hover:text-blue-400 font-medium">
                    Sign in
                </a>
            </p>
        </div>
    </div>
</div>
@endsection
