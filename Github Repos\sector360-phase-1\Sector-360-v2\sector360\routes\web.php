<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Dashboard
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Placeholder routes for navigation (we'll implement these next)
Route::get('/listings', function () {
    return redirect()->route('home')->with('message', 'Listings page coming soon!');
})->name('listings.index');

Route::get('/listings/{listing}', function () {
    return redirect()->route('home')->with('message', 'Listing details page coming soon!');
})->name('listings.show');

Route::get('/industries', function () {
    return redirect()->route('home')->with('message', 'Industries page coming soon!');
})->name('industries.index');

Route::get('/industries/{industry}', function () {
    return redirect()->route('home')->with('message', 'Industry page coming soon!');
})->name('industries.show');

Route::get('/brands', function () {
    return redirect()->route('home')->with('message', 'Brands page coming soon!');
})->name('brands.index');

Route::get('/brands/{brand}', function () {
    return redirect()->route('home')->with('message', 'Brand page coming soon!');
})->name('brands.show');

// Placeholder routes for authenticated users
Route::middleware('auth')->group(function () {
    Route::get('/listings/create', function () {
        return redirect()->route('dashboard')->with('message', 'Create listing page coming soon!');
    })->name('listings.create');

    Route::get('/my-listings', function () {
        return redirect()->route('dashboard')->with('message', 'My listings page coming soon!');
    })->name('listings.my');

    Route::get('/favorites', function () {
        return redirect()->route('dashboard')->with('message', 'Favorites page coming soon!');
    })->name('favorites.index');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
