<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\ListingController;
use App\Http\Controllers\QuoteRequestController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Dashboard
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Listing routes
Route::get('/listings', [ListingController::class, 'index'])->name('listings.index');
Route::get('/listings/{listing}', [ListingController::class, 'show'])->name('listings.show');

Route::get('/industries', function () {
    return redirect()->route('home')->with('message', 'Industries page coming soon!');
})->name('industries.index');

Route::get('/industries/{industry}', function () {
    return redirect()->route('home')->with('message', 'Industry page coming soon!');
})->name('industries.show');

Route::get('/brands', function () {
    return redirect()->route('home')->with('message', 'Brands page coming soon!');
})->name('brands.index');

Route::get('/brands/{brand}', function () {
    return redirect()->route('home')->with('message', 'Brand page coming soon!');
})->name('brands.show');

// Authenticated listing routes
Route::middleware('auth')->group(function () {
    Route::get('/listings/create', [ListingController::class, 'create'])->name('listings.create');
    Route::post('/listings', [ListingController::class, 'store'])->name('listings.store');
    Route::get('/listings/{listing}/edit', [ListingController::class, 'edit'])->name('listings.edit');
    Route::put('/listings/{listing}', [ListingController::class, 'update'])->name('listings.update');
    Route::delete('/listings/{listing}', [ListingController::class, 'destroy'])->name('listings.destroy');

    Route::get('/my-listings', function () {
        return redirect()->route('dashboard')->with('message', 'My listings page coming soon!');
    })->name('listings.my');

    Route::get('/favorites', function () {
        return redirect()->route('dashboard')->with('message', 'Favorites page coming soon!');
    })->name('favorites.index');

    // Quote Request routes
    Route::post('/quote-requests', [QuoteRequestController::class, 'store'])->name('quote-requests.store');
    Route::get('/quote-requests', [QuoteRequestController::class, 'index'])->name('quote-requests.index');
    Route::put('/quote-requests/{quoteRequest}', [QuoteRequestController::class, 'update'])->name('quote-requests.update');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
