{"name": "laravel/breeze", "description": "Minimal Laravel authentication scaffolding with Blade and Tailwind.", "keywords": ["laravel", "auth"], "license": "MIT", "support": {"issues": "https://github.com/laravel/breeze/issues", "source": "https://github.com/laravel/breeze"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1.0", "illuminate/console": "^10.17", "illuminate/filesystem": "^10.17", "illuminate/support": "^10.17", "illuminate/validation": "^10.17"}, "autoload": {"psr-4": {"Laravel\\Breeze\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}, "laravel": {"providers": ["Laravel\\Breeze\\BreezeServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "require-dev": {"orchestra/testbench": "^8.0", "phpstan/phpstan": "^1.10"}}