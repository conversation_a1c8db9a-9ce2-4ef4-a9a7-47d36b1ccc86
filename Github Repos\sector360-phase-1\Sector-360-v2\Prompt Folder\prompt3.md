# Sector360 Marketplace Demo - Part 3: UI/UX Design System

## Tailwind Configuration

```javascript
// tailwind.config.js
module.exports = {
    darkMode: 'class',
    content: [
        './resources/**/*.blade.php',
        './resources/**/*.js',
        './resources/**/*.vue',
    ],
    theme: {
        extend: {
            colors: {
                'sector': {
                    'bg': '#0f0f0f',
                    'card': '#1a1a1a',
                    'border': '#2a2a2a',
                    'primary': '#2563eb',
                    'primary-hover': '#1d4ed8',
                    'accent': '#10b981',
                    'danger': '#ef4444',
                    'warning': '#f59e0b',
                },
            },
            fontFamily: {
                'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
            },
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
        require('@tailwindcss/aspect-ratio'),
    ],
}
```

## Main Layout Template

```blade
{{-- resources/views/layouts/app.blade.php --}}
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Sector360 - The World\'s Business Marketplace')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="//unpkg.com/alpinejs" defer></script>
    
    @stack('styles')
</head>
<body class="bg-sector-bg text-gray-300 antialiased">
    <!-- Navigation -->
    <nav class="bg-sector-card border-b border-sector-border sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sector-primary rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xl">S360</span>
                        </div>
                        <span class="text-white font-semibold text-lg">Sector360</span>
                    </a>
                </div>
                
                <!-- Main Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('listings.index') }}" class="text-gray-300 hover:text-white transition">Buy</a>
                    <a href="{{ route('listings.create') }}" class="text-gray-300 hover:text-white transition">Sell</a>
                    <a href="{{ route('industries.index') }}" class="text-gray-300 hover:text-white transition">Browse by Industry</a>
                    <a href="{{ route('brands.index') }}" class="text-gray-300 hover:text-white transition">Browse by Brand</a>
                </div>
                
                <!-- Right Navigation -->
                <div class="flex items-center space-x-4">
                    @auth
                        <x-user-dropdown />
                    @else
                        <a href="{{ route('login') }}" class="text-gray-300 hover:text-white transition">Log In</a>
                        <a href="{{ route('register') }}" class="bg-sector-primary hover:bg-sector-primary-hover text-white px-4 py-2 rounded-lg transition">
                            Sign Up
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    <x-footer />

    @stack('scripts')
</body>
</html>
```

## UI Components

### Listing Card Component
```blade
{{-- resources/views/components/listing-card.blade.php --}}
<div class="bg-sector-card rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
    <a href="{{ route('listings.show', $listing) }}" class="block">
        <!-- Image Container -->
        <div class="relative aspect-w-16 aspect-h-12 bg-gray-800">
            @if($listing->images->count() > 0)
                <img src="{{ $listing->primaryImage->path }}" 
                     alt="{{ $listing->title }}"
                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                
                <!-- Image Count Badge -->
                @if($listing->images->count() > 1)
                    <div class="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                        <i class="fas fa-camera mr-1"></i>{{ $listing->images->count() }}
                    </div>
                @endif
            @else
                <div class="flex items-center justify-center h-full">
                    <i class="fas fa-image text-gray-600 text-4xl"></i>
                </div>
            @endif
            
            <!-- Condition Badge -->
            <div class="absolute top-2 left-2">
                <span class="px-2 py-1 text-xs font-semibold rounded 
                    {{ $listing->condition === 'new' ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white' }}">
                    {{ ucfirst($listing->condition) }}
                </span>
            </div>
        </div>
        
        <!-- Content -->
        <div class="p-4">
            <h3 class="text-white font-semibold mb-1 line-clamp-2 group-hover:text-sector-primary transition">
                {{ $listing->title }}
            </h3>
            
            <p class="text-gray-400 text-sm mb-2">{{ $listing->equipmentType->name }}</p>
            
            <div class="text-sm text-gray-500 mb-3">
                <div>{{ $listing->user->company_name }}</div>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-xl font-bold text-white">{{ $listing->formatted_price }}</div>
                </div>
                
                <div class="text-right text-sm text-gray-500">
                    <div>{{ $listing->city }}, {{ $listing->state_province }}</div>
                    <div>{{ $listing->time_ago }}</div>
                </div>
            </div>
        </div>
    </a>
</div>
```

### Search Filter Sidebar Component
```blade
{{-- resources/views/components/search-filters.blade.php --}}
<div class="bg-sector-card rounded-lg p-6 space-y-6">
    <!-- Location Filter -->
    <div>
        <h3 class="text-white font-semibold mb-3">Location</h3>
        <div class="space-y-2" x-data="{ showAll: false }">
            @foreach($countries as $country)
                <label class="flex items-center cursor-pointer" 
                       x-show="showAll || {{ $loop->index < 5 ? 'true' : 'false' }}">
                    <input type="checkbox" 
                           name="countries[]" 
                           value="{{ $country->country }}"
                           class="mr-2 rounded bg-gray-700 border-gray-600 text-sector-primary focus:ring-sector-primary">
                    <span class="text-gray-300">{{ $country->country }}</span>
                    <span class="text-gray-500 ml-auto">({{ $country->total }})</span>
                </label>
            @endforeach
            
            @if($countries->count() > 5)
                <button @click="showAll = !showAll" 
                        class="text-sector-primary hover:text-sector-primary-hover text-sm">
                    <span x-text="showAll ? 'Less...' : 'More...'"></span>
                </button>
            @endif
        </div>
    </div>

    <!-- Industry Filter -->
    <div>
        <h3 class="text-white font-semibold mb-3">Industry</h3>
        <div class="space-y-2" x-data="{ showAll: false }">
            @foreach($industries as $industry)
                <label class="flex items-center cursor-pointer"
                       x-show="showAll || {{ $loop->index < 5 ? 'true' : 'false' }}">
                    <input type="checkbox" 
                           name="industries[]" 
                           value="{{ $industry->id }}"
                           class="mr-2 rounded bg-gray-700 border-gray-600 text-sector-primary focus:ring-sector-primary">
                    <span class="text-gray-300">{{ $industry->name }}</span>
                    <span class="text-gray-500 ml-auto">({{ $industry->listing_count }})</span>
                </label>
            @endforeach
            
            @if($industries->count() > 5)
                <button @click="showAll = !showAll" 
                        class="text-sector-primary hover:text-sector-primary-hover text-sm">
                    <span x-text="showAll ? 'Less...' : 'More...'"></span>
                </button>
            @endif
        </div>
    </div>

    <!-- Price Range -->
    <div>
        <h3 class="text-white font-semibold mb-3">Price Range</h3>
        <div class="space-y-2">
            <input type="number" 
                   name="price_min" 
                   placeholder="Min" 
                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400">
            <input type="number" 
                   name="price_max" 
                   placeholder="Max" 
                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400">
        </div>
    </div>

    <!-- Condition -->
    <div>
        <h3 class="text-white font-semibold mb-3">Condition</h3>
        <div class="space-y-2">
            <label class="flex items-center cursor-pointer">
                <input type="checkbox" name="condition[]" value="new" 
                       class="mr-2 rounded bg-gray-700 border-gray-600 text-sector-primary focus:ring-sector-primary">
                <span class="text-gray-300">New</span>
            </label>
            <label class="flex items-center cursor-pointer">
                <input type="checkbox" name="condition[]" value="used" 
                       class="mr-2 rounded bg-gray-700 border-gray-600 text-sector-primary focus:ring-sector-primary">
                <span class="text-gray-300">Used</span>
            </label>
        </div>
    </div>

    <!-- Apply Filters Button -->
    <button type="submit" 
            class="w-full bg-sector-primary hover:bg-sector-primary-hover text-white py-2 rounded-lg transition">
        Apply Filters
    </button>
</div>
```

### Pagination Component
```blade
{{-- resources/views/components/pagination.blade.php --}}
@if ($paginator->hasPages())
    <nav class="flex items-center justify-center space-x-1">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <span class="px-3 py-2 text-gray-500 bg-sector-card rounded">Previous</span>
        @else
            <a href="{{ $paginator->previousPageUrl() }}" 
               class="px-3 py-2 bg-sector-card hover:bg-gray-700 text-white rounded transition">
                Previous
            </a>
        @endif

        {{-- Pagination Elements --}}
        @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
                <span class="px-3 py-2 text-gray-500">{{ $element }}</span>
            @endif

            {{-- Array Of Links --}}
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <span class="px-3 py-2 bg-sector-primary text-white rounded">{{ $page }}</span>
                    @else
                        <a href="{{ $url }}" 
                           class="px-3 py-2 bg-sector-card hover:bg-gray-700 text-white rounded transition">
                            {{ $page }}
                        </a>
                    @endif
                @endforeach
            @endif
        @endforeach

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}" 
               class="px-3 py-2 bg-sector-card hover:bg-gray-700 text-white rounded transition">
                Next
            </a>
        @else
            <span class="px-3 py-2 text-gray-500 bg-sector-card rounded">Next</span>
        @endif
    </nav>
@endif
```
