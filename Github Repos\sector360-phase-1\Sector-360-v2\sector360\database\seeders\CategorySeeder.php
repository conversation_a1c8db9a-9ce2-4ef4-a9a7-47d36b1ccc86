<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $metalworking = \App\Models\Industry::where('slug', 'metalworking')->first();

        if (!$metalworking) {
            return;
        }

        $categories = [
            [
                'industry_id' => $metalworking->id,
                'name' => 'CNC Machines',
                'description' => 'Computer Numerical Control machines for precision manufacturing',
                'sort_order' => 1,
            ],
            [
                'industry_id' => $metalworking->id,
                'name' => 'Lathes',
                'description' => 'Turning machines for cylindrical parts',
                'sort_order' => 2,
            ],
            [
                'industry_id' => $metalworking->id,
                'name' => 'Mills',
                'description' => 'Milling machines for complex part manufacturing',
                'sort_order' => 3,
            ],
        ];

        foreach ($categories as $category) {
            \App\Models\Category::create($category);
        }
    }
}
