<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\WelcomeUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirect()
    {
        try {
            return Socialite::driver('google')->redirect();
        } catch (\Exception $e) {
            \Log::error('Google OAuth Redirect Error: ' . $e->getMessage());
            return redirect('/register')->with('error', 'Unable to connect to Google. Please try email registration.');
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function callback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if user already exists
            $user = User::where('email', $googleUser->getEmail())->first();

            if ($user) {
                // User exists, just log them in
                Auth::login($user);
                return redirect()->intended('/')->with('success', 'Welcome back!');
            } else {
                // Create new user
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'password' => Hash::make(uniqid()), // Random password since they use Google
                    'user_type' => 'buyer', // Default to buyer
                    'company_name' => 'Individual',
                    'google_id' => $googleUser->getId(),
                    'email_verified_at' => now(), // Google emails are pre-verified
                ]);

                // Send welcome email
                $user->notify(new WelcomeUser());

                Auth::login($user);
                return redirect('/')->with('success', 'Welcome to Sector360! Your account has been created.');
            }
        } catch (\Laravel\Socialite\Two\InvalidStateException $e) {
            \Log::error('Google OAuth Invalid State: ' . $e->getMessage());
            return redirect('/register')->with('error', 'Google authentication session expired. Please try again.');
        } catch (\Exception $e) {
            \Log::error('Google OAuth Error: ' . $e->getMessage());
            \Log::error('Google OAuth Stack Trace: ' . $e->getTraceAsString());
            return redirect('/register')->with('error', 'Unable to login with Google: ' . $e->getMessage());
        }
    }
}
