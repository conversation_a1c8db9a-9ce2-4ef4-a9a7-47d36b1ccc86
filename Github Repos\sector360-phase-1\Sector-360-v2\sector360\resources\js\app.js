import './bootstrap';

// Alpine.js
import Alpine from 'alpinejs';
import persist from '@alpinejs/persist';

// Supabase
import { createClient } from '@supabase/supabase-js';

Alpine.plugin(persist);

// Initialize Supabase client
const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.getAttribute('content');
const supabaseKey = document.querySelector('meta[name="supabase-anon-key"]')?.getAttribute('content');

if (supabaseUrl && supabaseKey) {
    window.supabase = createClient(supabaseUrl, supabaseKey);
}

window.Alpine = Alpine;

Alpine.start();
