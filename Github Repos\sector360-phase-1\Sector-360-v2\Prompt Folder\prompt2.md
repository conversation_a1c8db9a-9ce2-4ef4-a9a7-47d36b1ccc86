# Sector360 Marketplace Demo - Part 2: Core Models

## Models and Relationships

### User Model
```php
// app/Models/User.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'company_name', 'phone', 
        'location', 'address', 'user_type', 'is_verified'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'is_verified' => 'boolean',
    ];

    public function listings()
    {
        return $this->hasMany(Listing::class);
    }

    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    public function favorites()
    {
        return $this->belongsToMany(Listing::class, 'favorites')->withTimestamps();
    }

    public function savedSearches()
    {
        return $this->hasMany(SavedSearch::class);
    }

    public function isSeller()
    {
        return $this->user_type === 'seller' || $this->hasRole('seller');
    }

    public function isAdmin()
    {
        return $this->user_type === 'admin' || $this->hasRole('admin');
    }

    public function isBuyer()
    {
        return $this->user_type === 'buyer' || $this->hasRole('buyer');
    }
}
```

### Listing Model
```php
// app/Models/Listing.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Listing extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'user_id', 'title', 'brand_id', 'model', 'year', 'condition',
        'price', 'currency', 'location', 'city', 'state_province', 'country',
        'description', 'specifications', 'specifications_metric', 'stock_number', 
        'industry_id', 'category_id', 'equipment_type_id', 'group_id', 
        'status', 'control_type', 'additional_features', 'featured'
    ];

    protected $casts = [
        'specifications' => 'array',
        'specifications_metric' => 'array',
        'featured' => 'boolean',
        'year' => 'integer',
        'price' => 'decimal:2',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom(['title', 'stock_number'])
            ->saveSlugsTo('slug');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function industry()
    {
        return $this->belongsTo(Industry::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function equipmentType()
    {
        return $this->belongsTo(EquipmentType::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function images()
    {
        return $this->hasMany(ListingImage::class)->orderBy('sort_order');
    }

    public function primaryImage()
    {
        return $this->hasOne(ListingImage::class)->where('is_primary', true);
    }

    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    public function favoritedBy()
    {
        return $this->belongsToMany(User::class, 'favorites')->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'For Sale');
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeInIndustry($query, $industryId)
    {
        return $query->where('industry_id', $industryId);
    }

    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByBrand($query, $brandId)
    {
        return $query->where('brand_id', $brandId);
    }

    public function scopeWithinPriceRange($query, $min, $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    // Accessors
    public function getPrimaryImageUrlAttribute()
    {
        return $this->primaryImage?->path ?? '/images/placeholder-equipment.jpg';
    }

    public function getFormattedPriceAttribute()
    {
        if (!$this->price) {
            return 'Call for Price';
        }
        return '$' . number_format($this->price, 0, '.', ',');
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    // Methods
    public function incrementViews()
    {
        $this->increment('views');
    }

    public function isFavoritedBy(User $user)
    {
        return $this->favoritedBy()->where('user_id', $user->id)->exists();
    }

    public function getSimilarListings($limit = 12)
    {
        return static::where('id', '!=', $this->id)
            ->where('category_id', $this->category_id)
            ->active()
            ->with(['primaryImage', 'user'])
            ->limit($limit)
            ->inRandomOrder()
            ->get();
    }
}
```

### Industry Model
```php
// app/Models/Industry.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Industry extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = ['name', 'slug', 'icon_class', 'description', 'sort_order', 'is_active'];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function categories()
    {
        return $this->hasMany(Category::class);
    }

    public function groups()
    {
        return $this->hasMany(Group::class);
    }

    public function listings()
    {
        return $this->hasMany(Listing::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)->orderBy('sort_order');
    }

    public function getListingCountAttribute()
    {
        return $this->listings()->active()->count();
    }
}
