# Sector360 Marketplace Demo - Part 4: Page Templates

## Dependencies & Notes

**Required Dependencies:**
- Alpine.js (for interactive components)
- Components defined in prompt3.md: `x-search-filters`, `x-listing-card`, `components.pagination`
- Controllers must provide: `$recentListings`, `$countries`, `$industries`, `$listings` with proper eager loading
- Listing model requires accessors: `formatted_price`, `time_ago`
- Listing relationships: `primaryImage`, `images`, `industry`, `category`, `user`

## Homepage

```blade
{{-- resources/views/home.blade.php --}}
@extends('layouts.app')

@section('content')
<!-- Search Section -->
<section class="bg-sector-card border-b border-sector-border py-8">
    <div class="container mx-auto px-4">
        <h1 class="text-2xl font-bold text-white mb-6 text-center">Buy & Sell Equipment</h1>
        
        <!-- Search Bar -->
        <form action="{{ route('listings.search') }}" method="GET" class="max-w-3xl mx-auto">
            @csrf
            <div class="flex gap-2">
                <input type="text" 
                       name="q" 
                       placeholder="Search Make, Model, or Category"
                       class="flex-1 px-4 py-3 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                <button type="submit" class="bg-sector-primary hover:bg-sector-primary-hover text-white px-6 py-3 rounded-lg transition font-medium">
                    Go
                </button>
            </div>
            
            <!-- Condition Filter -->
            <div class="flex items-center justify-center gap-4 mt-4">
                <label class="flex items-center cursor-pointer">
                    <input type="radio" name="condition" value="all" checked class="sr-only peer">
                    <span class="px-4 py-2 rounded-md text-gray-400 peer-checked:bg-sector-primary peer-checked:text-white transition">All</span>
                </label>
                <label class="flex items-center cursor-pointer">
                    <input type="radio" name="condition" value="used" class="sr-only peer">
                    <span class="px-4 py-2 rounded-md text-gray-400 peer-checked:bg-sector-primary peer-checked:text-white transition">Used</span>
                </label>
                <label class="flex items-center cursor-pointer">
                    <input type="radio" name="condition" value="new" class="sr-only peer">
                    <span class="px-4 py-2 rounded-md text-gray-400 peer-checked:bg-sector-primary peer-checked:text-white transition">New</span>
                </label>
            </div>
        </form>
    </div>
</section>

<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Left Sidebar Filters -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Location Filter -->
            {{ ... }}
            
            <!-- Industry Filter -->
            <div class="bg-sector-card rounded-lg p-4">
                <h3 class="font-semibold text-white mb-3">Industry</h3>
                <div class="space-y-2 text-sm">
                    @foreach($industries as $industry)
                        <a href="{{ route('industries.show', $industry) }}" 
                           class="flex items-center justify-between p-2 hover:bg-gray-800 rounded transition">
                            <span class="text-gray-300">{{ $industry->name }}</span>
                            <span class="text-gray-500 text-xs">({{ $industry->listings_count }})</span>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Browse by Industry -->
            <section class="mb-8">
                <h2 class="text-xl font-bold text-white mb-4">Browse by Industry</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($industries as $industry)
                        <a href="{{ route('industries.show', $industry) }}" 
                           class="bg-sector-card hover:bg-gray-800 rounded-lg p-4 text-center transition-all duration-200 group">
                            <div class="text-4xl mb-2">{{ $industry->icon }}</div>
                            <h3 class="text-sm font-medium text-white group-hover:text-sector-primary">
                                {{ $industry->name }}
                            </h3>
                        </a>
                    @endforeach
                </div>
            </section>
            
            <!-- Recent Equipment -->
            <section>
                <h2 class="text-xl font-bold text-white mb-4">Recent Equipment</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @forelse($recentListings as $listing)
                        <div class="bg-sector-card rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                            <div class="aspect-w-16 aspect-h-12 bg-gray-800">
                                @if($listing->primaryImage)
                                    <img src="{{ $listing->primaryImage->path }}" 
                                         alt="{{ $listing->title }}"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                @else
                                    <img src="/images/no-image-placeholder.png" 
                                         alt="No image available"
                                         class="w-full h-full object-cover">
                                @endif
                                <div class="absolute top-2 left-2">
                                    <span class="bg-{{ $listing->condition == 'New' ? 'green' : 'blue' }}-600 text-white px-2 py-1 rounded text-xs font-medium">
                                        {{ $listing->condition }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="p-4">
                                <h3 class="font-semibold text-white mb-1 line-clamp-2">{{ $listing->title }}</h3>
                                <p class="text-gray-400 text-sm mb-2">{{ $listing->brand->name ?? 'Unknown Brand' }} • {{ $listing->year }}</p>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-sector-primary">{{ $listing->formatted_price }}</div>
                                    <div class="text-xs text-gray-500">{{ $listing->time_ago }}</div>
                                </div>
                                <div class="mt-2 text-xs text-gray-500">
                                    <i class="fas fa-map-marker-alt"></i> {{ $listing->city }}, {{ $listing->state_province }}
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-span-full text-center py-12">
                            <p class="text-gray-400">No recent listings available</p>
                        </div>
                    @endforelse
                </div>
            </section>
        </div>
    </div>
</div>
@endsection
```

## Search Results Page

```blade
{{-- resources/views/listings/search.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Search Header -->
    @if(request('q'))
        <h1 class="text-2xl font-bold text-white mb-2">Search results for "{{ request('q') }}"</h1>
        <p class="text-gray-400 mb-6">{{ $listings->total() }} listings found</p>
    @endif

    <div class="flex gap-8">
        <!-- Left Sidebar Filters -->
        <aside class="w-64 flex-shrink-0 hidden lg:block">
            <form action="{{ route('listings.search') }}" method="GET">
                @csrf
                <input type="hidden" name="q" value="{{ request('q') }}">
                
                {{-- Note: x-search-filters component defined in prompt3.md --}}
                <x-search-filters 
                    :countries="$countries"
                    :industries="$industries" />
            </form>
        </aside>
        
        <!-- Main Content -->
        <div class="flex-1">
            <!-- Sort Bar -->
            <div class="flex items-center justify-between mb-6">
                <div class="text-gray-400">
                    Showing {{ $listings->firstItem() ?? 0 }}-{{ $listings->lastItem() ?? 0 }} of {{ $listings->total() }} results
                </div>
                
                <form action="{{ route('listings.search') }}" method="GET" class="inline-block">
                    @csrf
                    <input type="hidden" name="q" value="{{ request('q') }}">
                    @foreach(request()->except(['sort', 'page']) as $key => $value)
                        <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                    @endforeach
                    <select name="sort" onchange="this.form.submit()" 
                            class="bg-sector-card border border-sector-border rounded-lg px-4 py-2 text-gray-300">
                        <option value="newest" @selected(request('sort') == 'newest')>Newest First</option>
                        <option value="price_low" @selected(request('sort') == 'price_low')>Price: Low to High</option>
                        <option value="price_high" @selected(request('sort') == 'price_high')>Price: High to Low</option>
                        <option value="year_new" @selected(request('sort') == 'year_new')>Year: Newest First</option>
                    </select>
                </form>
            </div>
            
            <!-- Listings Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($listings as $listing)
                    {{-- Note: x-listing-card component defined in prompt3.md --}}
                    <x-listing-card :listing="$listing" />
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-400 text-xl">No listings found matching your criteria.</p>
                        <a href="{{ route('listings.index') }}" 
                           class="text-sector-primary hover:text-sector-primary-hover mt-4 inline-block">
                            View all listings
                        </a>
                    </div>
                @endforelse
            </div>
            
            <!-- Pagination -->
            @if($listings->hasPages())
                <div class="mt-8">
                    {{-- Note: components.pagination view defined in prompt3.md --}}
                    {{ $listings->withQueryString()->links('components.pagination') }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
```

## Listing Detail Page

```blade
{{-- resources/views/listings/show.blade.php --}}
@extends('layouts.app')

@section('title', $listing->title . ' - Sector360')

@section('content')
<!-- Breadcrumbs -->
<div class="bg-sector-card border-b border-sector-border">
    <div class="container mx-auto px-4 py-3">
        <nav class="flex text-sm text-gray-400">
            <a href="/" class="hover:text-white">Home</a>
            <span class="mx-2">/</span>
            <a href="{{ route('industries.show', $listing->industry) }}" class="hover:text-white">
                {{ $listing->industry->name }}
            </a>
            <span class="mx-2">/</span>
            <a href="{{ route('categories.show', $listing->category) }}" class="hover:text-white">
                {{ $listing->category->name }}
            </a>
            <span class="mx-2">/</span>
            <span class="text-white">{{ $listing->title }}</span>
        </nav>
    </div>
</div>

<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Image Gallery -->
            <div class="bg-sector-card rounded-lg p-6 mb-6">
                <div class="aspect-w-16 aspect-h-12 mb-4">
                    @if($listing->primaryImage)
                        <img id="mainImage" 
                             src="{{ $listing->primaryImage->path }}" 
                             alt="{{ $listing->title }}"
                             class="w-full h-full object-contain rounded-lg">
                    @else
                        <img id="mainImage" 
                             src="/images/no-image-placeholder.png" 
                             alt="No image available"
                             class="w-full h-full object-contain rounded-lg">
                    @endif
                </div>
                
                @if($listing->images->count() > 1)
                    <div class="grid grid-cols-4 gap-2">
                        @foreach($listing->images as $image)
                            <img src="{{ $image->thumbnail_path }}" 
                                 data-full="{{ $image->path }}"
                                 alt="Image {{ $loop->iteration }}"
                                 class="w-full h-20 object-cover rounded cursor-pointer hover:opacity-75 transition"
                                 onclick="document.getElementById('mainImage').src = this.dataset.full">
                        @endforeach
                    </div>
                @endif
            </div>

            <!-- Description -->
            <div class="bg-sector-card rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-white mb-4">Description</h2>
                <div class="text-gray-300 prose prose-invert max-w-none">
                    {{ $listing->description }}
                </div>
            </div>

            <!-- Specifications -->
            <div class="bg-sector-card rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Specifications</h2>
                    <div class="flex gap-2" x-data="{ unit: 'imperial' }">
                        <button @click="unit = 'imperial'" 
                                :class="unit === 'imperial' ? 'bg-sector-primary text-white' : 'bg-gray-700 text-gray-400'"
                                class="px-3 py-1 rounded text-sm transition">
                            Imperial
                        </button>
                        <button @click="unit = 'metric'" 
                                :class="unit === 'metric' ? 'bg-sector-primary text-white' : 'bg-gray-700 text-gray-400'"
                                class="px-3 py-1 rounded text-sm transition">
                            Metric
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        @if($listing->specifications && count($listing->specifications) > 0)
                            @foreach($listing->specifications as $key => $value)
                                <tr class="border-b border-gray-700">
                                    <td class="py-2 text-gray-400">{{ $key }}</td>
                                    <td class="py-2 text-white font-medium">{{ $value }}</td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td class="py-4 text-gray-400 text-center" colspan="2">
                                    No specifications available
                                </td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Listing Info -->
            <div class="bg-sector-card rounded-lg p-6 mb-6 sticky top-4">
                <div class="mb-4">
                    <div class="text-3xl font-bold text-white">{{ $listing->formatted_price }}</div>
                    <div class="text-gray-400">{{ $listing->condition }} • {{ $listing->year }}</div>
                </div>
                
                <div class="space-y-3 mb-6 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Stock #</span>
                        <span class="text-white">{{ $listing->stock_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Location</span>
                        <span class="text-white">{{ $listing->city }}, {{ $listing->state_province }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Listed</span>
                        <span class="text-white">{{ $listing->time_ago }}</span>
                    </div>
                </div>
                
                <button onclick="openQuoteModal()" 
                        class="w-full bg-sector-primary hover:bg-sector-primary-hover text-white py-3 rounded-lg font-semibold transition mb-3">
                    Request Quote
                </button>
                
                <!-- Seller Info -->
                <div class="border-t border-gray-700 pt-4">
                    <h3 class="font-semibold text-white mb-2">{{ $listing->user->company_name }}</h3>
                    <p class="text-gray-400 text-sm">Member since {{ $listing->user->created_at->year }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quote Modal -->
<div id="quoteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-sector-card rounded-lg max-w-md w-full p-6">
        <h3 class="text-xl font-semibold text-white mb-4">Request Quote</h3>
        <form action="{{ route('quotes.store') }}" method="POST">
            @csrf
            <input type="hidden" name="listing_id" value="{{ $listing->id }}">
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Your Message</label>
                    <textarea name="message" rows="4" 
                              class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary"
                              placeholder="I'm interested in this equipment..."></textarea>
                </div>
                
                @guest
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Name</label>
                        <input type="text" name="name" required
                               class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                        <input type="email" name="email" required
                               class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Phone</label>
                        <input type="tel" name="phone"
                               class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                    </div>
                @endguest
            </div>
            
            <div class="mt-6 flex gap-3">
                <button type="submit" 
                        class="flex-1 bg-sector-primary hover:bg-sector-primary-hover text-white py-2 rounded-lg font-medium transition">
                    Send Request
                </button>
                <button type="button" onclick="closeQuoteModal()" 
                        class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 rounded-lg font-medium transition">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openQuoteModal() {
    document.getElementById('quoteModal').classList.remove('hidden');
}

function closeQuoteModal() {
    document.getElementById('quoteModal').classList.add('hidden');
}

// Close modal on outside click
document.getElementById('quoteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuoteModal();
    }
});
</script>
@endsection

## Add Equipment Page

```blade
{{-- resources/views/listings/create.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="bg-sector-card border-b border-sector-border">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-white">Add Equipment</h1>
    </div>
</div>

<div class="container mx-auto px-4 py-8 max-w-4xl">
    <form action="{{ route('listings.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        
        <!-- Basic Information -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Manufacturer -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Manufacturer *</label>
                    <select name="manufacturer_id" required 
                            class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white focus:outline-none focus:border-sector-primary">
                        <option value="">Select Manufacturer</option>
                        @foreach($manufacturers as $manufacturer)
                            <option value="{{ $manufacturer->id }}">{{ $manufacturer->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Model -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Model</label>
                    <input type="text" name="model" 
                           placeholder="Enter model"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Type *</label>
                    <select name="equipment_type_id" required 
                            class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white focus:outline-none focus:border-sector-primary">
                        <option value="">Select Equipment Type</option>
                        @foreach($equipmentTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Stock Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Stock #</label>
                    <input type="text" name="stock_number" 
                           value="{{ $generatedStockNumber }}" 
                           placeholder="Auto-generated"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Serial Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Serial #</label>
                    <input type="text" name="serial_number" 
                           placeholder="Enter serial number"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Year -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Year</label>
                    <input type="number" name="year" 
                           min="1900" max="{{ date('Y') + 1 }}"
                           placeholder="YYYY"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Condition -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Condition *</label>
                    <div class="flex gap-4">
                        <label class="flex items-center cursor-pointer">
                            <input type="radio" name="condition" value="used" checked class="sr-only peer">
                            <span class="px-4 py-2 rounded-md bg-gray-700 text-gray-400 peer-checked:bg-sector-primary peer-checked:text-white transition">Used</span>
                        </label>
                        <label class="flex items-center cursor-pointer">
                            <input type="radio" name="condition" value="new" class="sr-only peer">
                            <span class="px-4 py-2 rounded-md bg-gray-700 text-gray-400 peer-checked:bg-sector-primary peer-checked:text-white transition">New</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Title -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                <input type="text" name="title" required
                       placeholder="e.g., 2020 HAAS VF-2SS CNC Vertical Machining Center"
                       class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
            </div>
            
            <!-- Description -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea name="description" rows="5"
                          placeholder="Provide detailed information about the equipment..."
                          class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary"></textarea>
            </div>
        </div>
        
        <!-- Media -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Media</h2>
            
            <!-- Images -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Images</label>
                <div class="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
                    <input type="file" name="images[]" multiple accept="image/*" class="hidden" id="imageUpload">
                    <label for="imageUpload" class="cursor-pointer">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-500 mb-2"></i>
                        <p class="text-gray-400">Select or Drag Media Files to Attach</p>
                        <p class="text-sm text-gray-500 mt-1">Max 10 images, 5MB each</p>
                    </label>
                </div>
            </div>
            
            <!-- Video URL -->
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Video URL</label>
                <input type="url" name="video_url" 
                       placeholder="Add YouTube or Vimeo video URL"
                       class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
            </div>
        </div>
        
        <!-- Pricing -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Pricing</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Sale Price -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Price *
                        <label class="inline-flex items-center ml-4">
                            <input type="checkbox" name="price_visible" checked class="mr-2">
                            <span class="text-xs">Visible</span>
                        </label>
                    </label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">$</span>
                        <input type="number" name="price" required
                               min="0" step="0.01"
                               placeholder="0.00"
                               class="w-full pl-8 pr-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                    </div>
                </div>
                
                <!-- Quantity -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Quantity</label>
                    <input type="number" name="quantity" 
                           min="1" value="1"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
            </div>
        </div>
        
        <!-- Specifications -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">
                Specifications
                <label class="inline-flex items-center ml-4">
                    <input type="checkbox" name="specs_visible" checked class="mr-2">
                    <span class="text-xs font-normal">Visible</span>
                </label>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Control -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Control
                        <label class="inline-flex items-center ml-2">
                            <input type="checkbox" name="is_cnc" class="mr-1">
                            <span class="text-xs">CNC Control?</span>
                        </label>
                    </label>
                    <select name="control_type" 
                            class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white focus:outline-none focus:border-sector-primary">
                        <option value="">Select Control</option>
                        <option value="fanuc">Fanuc</option>
                        <option value="siemens">Siemens</option>
                        <option value="haas">Haas</option>
                        <option value="mazak">Mazak</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <!-- Dimensions -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Dimensions</label>
                    <input type="text" name="dimensions" 
                           placeholder="e.g., 48 x 24 x 20 inches"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Weight -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Weight</label>
                    <input type="text" name="weight" 
                           placeholder="e.g., 5,000 lbs"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
            </div>
            
            <!-- Equipped With -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Equipped With</label>
                <div id="accessories" class="space-y-2">
                    <div class="flex gap-2">
                        <input type="text" name="accessories[]" 
                               placeholder="Add accessory"
                               class="flex-1 px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                        <button type="button" onclick="addAccessory()" 
                                class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Spec Sheet -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">
                    Spec Sheet
                    <label class="inline-flex items-center ml-4">
                        <input type="checkbox" name="spec_sheet_visible" checked class="mr-2">
                        <span class="text-xs">Visible</span>
                    </label>
                </label>
                <textarea name="spec_sheet" rows="8"
                          placeholder="Enter detailed specifications..."
                          class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary font-mono text-sm"></textarea>
            </div>
        </div>
        
        <!-- Location -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">
                Location
                <label class="inline-flex items-center ml-4">
                    <input type="checkbox" name="location_visible" checked class="mr-2">
                    <span class="text-xs font-normal">Visible</span>
                </label>
                <label class="inline-flex items-center ml-4">
                    <input type="checkbox" name="use_owner_location" checked class="mr-2">
                    <span class="text-xs font-normal">Use owner's location</span>
                </label>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Location Name -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Location Name</label>
                    <input type="text" name="location_name" 
                           value="{{ auth()->user()->company_name }}"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Phone -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Phone</label>
                    <input type="tel" name="phone" 
                           value="{{ auth()->user()->phone }}"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- Country -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Country</label>
                    <select name="country" 
                            class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white focus:outline-none focus:border-sector-primary">
                        <option value="US" selected>United States</option>
                        <option value="CA">Canada</option>
                        <option value="MX">Mexico</option>
                    </select>
                </div>
                
                <!-- Address -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Address Line 1</label>
                    <input type="text" name="address_line_1" 
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Address Line 2</label>
                    <input type="text" name="address_line_2" 
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- City -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">City</label>
                    <input type="text" name="city" 
                           value="{{ auth()->user()->city }}"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- State -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">State/Province</label>
                    <input type="text" name="state_province" 
                           value="{{ auth()->user()->state }}"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
                
                <!-- ZIP -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">ZIP/Postal Code</label>
                    <input type="text" name="zip" 
                           value="{{ auth()->user()->zip }}"
                           class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
                </div>
            </div>
        </div>
        
        <!-- Marketing Channels -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Marketing Channels</h2>
            
            <div class="space-y-3">
                <label class="flex items-center">
                    <input type="checkbox" name="channels[]" value="sector360" checked class="mr-3">
                    <span class="text-gray-300">Sector360 Marketplace</span>
                    <span class="text-sm text-gray-500 ml-2">- List your equipment for Free!</span>
                </label>
                
                <label class="flex items-center">
                    <input type="checkbox" name="channels[]" value="website" class="mr-3">
                    <span class="text-gray-300">Your Website</span>
                </label>
            </div>
        </div>
        
        <!-- Internal Notes -->
        <div class="bg-sector-card rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Internal Notes</h2>
            
            <textarea name="notes" rows="4"
                      placeholder="Add any internal notes (not visible to buyers)..."
                      class="w-full px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary"></textarea>
        </div>
        
        <!-- Submit Buttons -->
        <div class="flex justify-end gap-4">
            <a href="{{ route('dashboard') }}" 
               class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition">
                Cancel
            </a>
            <button type="submit" name="action" value="save_draft" 
                    class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition">
                Save as Draft
            </button>
            <button type="submit" name="action" value="publish" 
                    class="px-6 py-3 bg-sector-primary hover:bg-sector-primary-hover text-white rounded-lg font-medium transition">
                Publish Listing
            </button>
        </div>
    </form>
</div>

<script>
function addAccessory() {
    const container = document.getElementById('accessories');
    const newInput = document.createElement('div');
    newInput.className = 'flex gap-2';
    newInput.innerHTML = `
        <input type="text" name="accessories[]" 
               placeholder="Add accessory"
               class="flex-1 px-3 py-2 bg-sector-bg border border-sector-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-sector-primary">
        <button type="button" onclick="this.parentElement.remove()" 
                class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newInput);
}

// Image preview functionality
document.getElementById('imageUpload').addEventListener('change', function(e) {
    const fileCount = e.target.files.length;
    const label = this.nextElementSibling;
    if (fileCount > 0) {
        label.querySelector('p').textContent = `${fileCount} file(s) selected`;
    }
});
</script>
@endsection
