<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ListingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $seller = \App\Models\User::where('email', '<EMAIL>')->first();
        $metalworking = \App\Models\Industry::where('slug', 'metalworking')->first();
        $haas = \App\Models\Brand::where('slug', 'haas')->first();
        $mazak = \App\Models\Brand::where('slug', 'mazak')->first();

        if (!$seller || !$metalworking) {
            return; // Skip if required data doesn't exist
        }

        $listings = [
            [
                'user_id' => $seller->id,
                'title' => '2019 Haas VF-2SS CNC Vertical Machining Center',
                'brand_id' => $haas?->id,
                'model' => 'VF-2SS',
                'year' => 2019,
                'condition' => 'used',
                'price' => 89500.00,
                'location' => 'Chicago, IL',
                'description' => 'Excellent condition Haas VF-2SS with low hours. Perfect for precision machining operations. Includes tooling and setup.',
                'stock_number' => 'HVF2SS001',
                'industry_id' => $metalworking->id,
                'category_id' => 1, // We'll create categories later
                'status' => 'For Sale',
                'control_type' => 'Haas NGC Control',
                'additional_features' => 'Automatic Tool Changer, Coolant System, Work Light',
                'featured' => true,
            ],
            [
                'user_id' => $seller->id,
                'title' => '2020 Mazak Quick Turn Nexus 250-II CNC Lathe',
                'brand_id' => $mazak?->id,
                'model' => 'Quick Turn Nexus 250-II',
                'year' => 2020,
                'condition' => 'used',
                'price' => 125000.00,
                'location' => 'Detroit, MI',
                'description' => 'Low-hour Mazak lathe in exceptional condition. Ideal for high-precision turning operations.',
                'stock_number' => 'MQT250001',
                'industry_id' => $metalworking->id,
                'category_id' => 1,
                'status' => 'For Sale',
                'control_type' => 'Mazatrol SmoothX CNC',
                'additional_features' => 'Live Tooling, Sub-Spindle, Bar Feeder Ready',
                'featured' => true,
            ],
            [
                'user_id' => $seller->id,
                'title' => '2018 DMG Mori NLX 2500SY CNC Turning Center',
                'brand_id' => \App\Models\Brand::where('slug', 'dmg-mori')->first()?->id,
                'model' => 'NLX 2500SY',
                'year' => 2018,
                'condition' => 'used',
                'price' => 185000.00,
                'location' => 'Cleveland, OH',
                'description' => 'High-performance turning center with Y-axis capability. Perfect for complex parts manufacturing.',
                'stock_number' => 'DMG2500001',
                'industry_id' => $metalworking->id,
                'category_id' => 1,
                'status' => 'For Sale',
                'control_type' => 'Siemens 840D sl',
                'additional_features' => 'Y-Axis, Live Tooling, Automatic Part Catcher',
                'featured' => true,
            ],
        ];

        foreach ($listings as $listing) {
            \App\Models\Listing::create($listing);
        }
    }
}
