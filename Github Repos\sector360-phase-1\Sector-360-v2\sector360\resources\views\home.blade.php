@extends('layouts.app')

@section('title', 'Sector360 - The World\'s Business Marketplace')
@section('description', 'Find and sell industrial equipment on the world\'s leading business marketplace. CNC machines, metalworking equipment, and more.')

@section('content')
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-sector-bg via-sector-card to-sector-bg py-20">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-sector-text mb-6">
                The World's <span class="text-sector-primary">Business Marketplace</span>
            </h1>
            <p class="text-xl text-sector-text-muted mb-8 max-w-3xl mx-auto">
                Connect with buyers and sellers of industrial equipment worldwide. From CNC machines to metalworking equipment, find everything you need to power your business.
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto mb-8">
                <form method="GET" action="{{ route('listings.index') }}">
                    <div class="relative">
                        <input type="text"
                               name="search"
                               placeholder="Search for equipment, brands, or categories..."
                               class="w-full bg-sector-card border border-sector-border rounded-xl px-6 py-4 pl-14 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent text-lg">
                        <i class="fas fa-search absolute left-5 top-1/2 transform -translate-y-1/2 text-sector-text-muted text-lg"></i>
                        <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary">
                            Search
                        </button>
                    </div>
                </form>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalListings) }}+</div>
                    <div class="text-sector-text-muted">Equipment Listings</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalBrands) }}+</div>
                    <div class="text-sector-text-muted">Trusted Brands</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalSellers) }}+</div>
                    <div class="text-sector-text-muted">Verified Sellers</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Industries Section -->
    <section class="py-16 bg-sector-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-sector-text mb-4">Browse by Industry</h2>
                <p class="text-sector-text-muted">Find equipment specific to your industry</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                @foreach($industries as $industry)
                    <a href="{{ route('industries.show', $industry) }}" 
                       class="card hover:border-sector-primary transition-all duration-200 text-center group">
                        <div class="w-16 h-16 bg-sector-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-sector-primary/20 transition-colors">
                            <i class="{{ $industry->icon_class }} text-2xl text-sector-primary"></i>
                        </div>
                        <h3 class="font-semibold text-sector-text mb-2">{{ $industry->name }}</h3>
                        <p class="text-sm text-sector-text-muted">{{ $industry->description }}</p>
                    </a>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Featured Listings Section -->
    @if($featuredListings->count() > 0)
    <section class="py-16 bg-sector-card">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold text-sector-text mb-4">Featured Equipment</h2>
                    <p class="text-sector-text-muted">Hand-picked premium equipment from verified sellers</p>
                </div>
                <a href="{{ route('listings.index') }}" class="btn-secondary">
                    View All Equipment
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($featuredListings as $listing)
                    <div class="card hover:border-sector-primary transition-all duration-200 group">
                        <!-- Image -->
                        <div class="aspect-video bg-sector-bg rounded-lg mb-4 overflow-hidden">
                            @if($listing->primaryImage)
                                <img src="{{ $listing->primaryImage->path }}" 
                                     alt="{{ $listing->title }}"
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <i class="fas fa-image text-4xl text-sector-text-muted"></i>
                                </div>
                            @endif
                        </div>
                        
                        <!-- Content -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="text-xs bg-sector-primary/10 text-sector-primary px-2 py-1 rounded">
                                    {{ $listing->industry->name }}
                                </span>
                                <span class="text-xs text-sector-text-muted">{{ $listing->time_ago }}</span>
                            </div>
                            
                            <h3 class="font-semibold text-sector-text group-hover:text-sector-primary transition-colors">
                                {{ $listing->title }}
                            </h3>
                            
                            <div class="flex items-center space-x-2 text-sm text-sector-text-muted">
                                @if($listing->brand)
                                    <span>{{ $listing->brand->name }}</span>
                                    <span>•</span>
                                @endif
                                <span>{{ $listing->location }}</span>
                            </div>
                            
                            <div class="flex items-center justify-between pt-2">
                                <div class="text-lg font-bold text-sector-primary">
                                    {{ $listing->formatted_price }}
                                </div>
                                <a href="{{ route('listings.show', $listing) }}" class="btn-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Popular Brands Section -->
    @if($popularBrands->count() > 0)
    <section class="py-16 bg-sector-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-sector-text mb-4">Popular Brands</h2>
                <p class="text-sector-text-muted">Shop equipment from industry-leading manufacturers</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                @foreach($popularBrands as $brand)
                    <a href="{{ route('brands.show', $brand) }}" 
                       class="card hover:border-sector-primary transition-all duration-200 text-center group">
                        <div class="h-16 flex items-center justify-center mb-4">
                            @if($brand->logo_url)
                                <img src="{{ $brand->logo_url }}" 
                                     alt="{{ $brand->name }}"
                                     class="max-h-12 max-w-full object-contain">
                            @else
                                <div class="text-2xl font-bold text-sector-text group-hover:text-sector-primary transition-colors">
                                    {{ $brand->name }}
                                </div>
                            @endif
                        </div>
                        <div class="text-sm text-sector-text-muted">
                            {{ $brand->listings_count }} listings
                        </div>
                    </a>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-sector-primary to-blue-600">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">Ready to Start Selling?</h2>
            <p class="text-blue-100 mb-8 text-lg">
                Join thousands of sellers reaching buyers worldwide. List your equipment today and connect with serious buyers.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('register') }}" class="bg-white text-sector-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Start Selling Today
                </a>
                <a href="{{ route('listings.index') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-sector-primary transition-colors">
                    Browse Equipment
                </a>
            </div>
        </div>
    </section>
@endsection
