@extends('layouts.app')

@section('title', 'Sector360 - The World\'s Business Marketplace')
@section('description', 'Find and sell industrial equipment on the world\'s leading business marketplace. CNC machines, metalworking equipment, and more.')

@section('content')
<div class="bg-sector-bg min-h-screen">
    <!-- Hero Section with Buy/Sell Toggle -->
    <section class="relative py-12 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-sector-text mb-4">
                    The World's Business <span class="text-sector-primary">Marketplace</span>
                </h1>
                <p class="text-lg text-sector-text-muted mb-8 max-w-2xl mx-auto">
                    Connect with buyers and sellers of industrial equipment worldwide.
                </p>
            </div>

            <!-- Buy/Sell Toggle -->
            <div class="flex justify-center mb-8">
                <div class="bg-sector-card border border-sector-border rounded-xl p-2 inline-flex">
                    <button id="buy-toggle" class="toggle-btn active px-8 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-search mr-2"></i>
                        Buy
                    </button>
                    <button id="sell-toggle" class="toggle-btn px-8 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Sell
                    </button>
                </div>
            </div>

            <!-- Buy Mode Content -->
            <div id="buy-mode" class="mode-content">
                <!-- Interactive World Map Section -->
                <div class="bg-sector-card rounded-xl border border-sector-border p-8 mb-8">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-semibold text-sector-text mb-2">Find Equipment by Region</h2>
                        <p class="text-sector-text-muted">Select a region to explore available industrial equipment</p>
                    </div>

                    <!-- Interactive SVG World Map -->
                    <div id="world-map-buy" class="w-full h-80 bg-sector-bg rounded-lg border border-sector-border mb-6 overflow-hidden relative">
                        <svg viewBox="0 0 1000 500" class="w-full h-full">
                            <!-- United States -->
                            <path class="country-path" data-country="US" data-name="United States"
                                  d="M200 200 L350 180 L380 220 L320 280 L180 260 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Canada -->
                            <path class="country-path" data-country="CA" data-name="Canada"
                                  d="M180 120 L400 100 L420 160 L350 180 L200 200 L160 140 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Mexico -->
                            <path class="country-path" data-country="MX" data-name="Mexico"
                                  d="M200 280 L320 280 L340 320 L280 340 L220 320 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Brazil -->
                            <path class="country-path" data-country="BR" data-name="Brazil"
                                  d="M300 350 L380 340 L400 400 L350 450 L280 440 L260 380 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- United Kingdom -->
                            <path class="country-path" data-country="GB" data-name="United Kingdom"
                                  d="M480 180 L500 170 L510 190 L495 200 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Germany -->
                            <path class="country-path" data-country="DE" data-name="Germany"
                                  d="M520 190 L540 185 L545 205 L525 210 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- France -->
                            <path class="country-path" data-country="FR" data-name="France"
                                  d="M500 200 L520 195 L525 215 L505 220 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Italy -->
                            <path class="country-path" data-country="IT" data-name="Italy"
                                  d="M530 220 L540 215 L545 245 L535 250 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Spain -->
                            <path class="country-path" data-country="ES" data-name="Spain"
                                  d="M480 220 L510 215 L515 235 L485 240 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Russia -->
                            <path class="country-path" data-country="RU" data-name="Russia"
                                  d="M550 120 L750 100 L780 180 L720 200 L580 190 L540 150 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- China -->
                            <path class="country-path" data-country="CN" data-name="China"
                                  d="M700 200 L780 190 L800 240 L750 280 L680 270 L660 220 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Japan -->
                            <path class="country-path" data-country="JP" data-name="Japan"
                                  d="M820 220 L840 210 L850 240 L835 250 L825 245 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- India -->
                            <path class="country-path" data-country="IN" data-name="India"
                                  d="M620 250 L680 240 L700 300 L650 320 L600 300 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- Australia -->
                            <path class="country-path" data-country="AU" data-name="Australia"
                                  d="M720 380 L820 370 L840 420 L780 440 L700 430 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>

                            <!-- South Africa -->
                            <path class="country-path" data-country="ZA" data-name="South Africa"
                                  d="M520 380 L580 375 L590 410 L550 420 Z"
                                  fill="#374151" stroke="#4b5563" stroke-width="1"/>
                        </svg>

                        <!-- Map Legend -->
                        <div class="absolute bottom-4 left-4 bg-sector-card/90 backdrop-blur-sm rounded-lg p-3 text-xs">
                            <div class="text-sector-text font-medium mb-1">Click countries to explore</div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-gray-600 rounded-sm"></div>
                                    <span class="text-sector-text-muted">Available</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-sector-primary rounded-sm"></div>
                                    <span class="text-sector-text-muted">Selected</span>
                                </div>
                            </div>
                        </div>

                        <!-- Country Tooltip -->
                        <div id="country-tooltip" class="absolute bg-sector-card border border-sector-border rounded-lg px-3 py-2 text-sm text-sector-text shadow-lg pointer-events-none opacity-0 transition-opacity duration-200 z-10">
                            <div id="tooltip-country"></div>
                            <div id="tooltip-listings" class="text-xs text-sector-text-muted"></div>
                        </div>
                    </div>

                    <!-- Quick Region Selection -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="US">
                            <div class="font-medium text-sector-text">🇺🇸 United States</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['us_listings'] ?? '95,794' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="CA">
                            <div class="font-medium text-sector-text">🇨🇦 Canada</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['ca_listings'] ?? '2,736' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="EU">
                            <div class="font-medium text-sector-text">🇪🇺 Europe</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['eu_listings'] ?? '1,847' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="ASIA">
                            <div class="font-medium text-sector-text">🌏 Asia Pacific</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['asia_listings'] ?? '892' }} listings</div>
                        </button>
                    </div>
                </div>

                <!-- Industry Selection (Hidden by default) -->
                <div id="industry-selection" class="bg-sector-card rounded-xl border border-sector-border p-8 hidden">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-sector-text mb-2">Select Industry</h3>
                        <p class="text-sector-text-muted">Choose your industry to see relevant equipment</p>
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="industries-grid">
                        @foreach($industries as $industry)
                        <button class="industry-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-center"
                                data-industry="{{ $industry->id }}" data-name="{{ $industry->name }}">
                            <div class="w-12 h-12 bg-sector-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="{{ $industry->icon ?? 'fas fa-industry' }} text-xl text-sector-primary"></i>
                            </div>
                            <div class="font-medium text-sector-text text-sm">{{ $industry->name }}</div>
                            <div class="text-xs text-sector-text-muted">{{ number_format($industry->listings_count ?? 0) }} items</div>
                        </button>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Sell Mode Content -->
            <div id="sell-mode" class="mode-content hidden">
                @auth
                    @if(auth()->user()->isSeller())
                        <!-- Quick Sell Form -->
                        <div class="max-w-4xl mx-auto">
                            <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-semibold text-sector-text mb-2">List Your Equipment</h2>
                                    <p class="text-sector-text-muted">Reach thousands of buyers worldwide</p>
                                </div>

                                <form method="POST" action="{{ route('listings.store') }}" enctype="multipart/form-data" class="space-y-6">
                                    @csrf

                                    <!-- Equipment Details -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Equipment Title</label>
                                            <input type="text" name="title" required
                                                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                   placeholder="e.g., 2019 Haas VF-4 CNC Machining Center">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Price</label>
                                            <input type="number" name="price" required
                                                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                   placeholder="Enter price in USD">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Industry</label>
                                            <select name="industry_id" required class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                                                <option value="">Select Industry</option>
                                                @foreach($industries as $industry)
                                                    <option value="{{ $industry->id }}">{{ $industry->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Brand</label>
                                            <select name="brand_id" class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                                                <option value="">Select Brand</option>
                                                @foreach($brands as $brand)
                                                    <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Year</label>
                                            <input type="number" name="year" min="1900" max="{{ date('Y') + 1 }}"
                                                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                   placeholder="Manufacturing year">
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-sector-text mb-2">Description</label>
                                        <textarea name="description" rows="4" required
                                                  class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                  placeholder="Describe your equipment's condition, features, and specifications..."></textarea>
                                    </div>

                                    <div class="flex justify-between items-center pt-6">
                                        <a href="{{ route('listings.create') }}" class="text-sector-primary hover:text-blue-400">
                                            Need more options? Use full listing form →
                                        </a>
                                        <button type="submit" class="btn-primary px-8 py-3">
                                            <i class="fas fa-plus mr-2"></i>
                                            List Equipment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    @else
                        <!-- Upgrade to Seller -->
                        <div class="max-w-2xl mx-auto text-center">
                            <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                                <i class="fas fa-store text-4xl text-sector-primary mb-4"></i>
                                <h2 class="text-2xl font-semibold text-sector-text mb-4">Become a Seller</h2>
                                <p class="text-sector-text-muted mb-6">
                                    Upgrade your account to start selling industrial equipment and reach buyers worldwide.
                                </p>
                                <a href="{{ route('profile.edit') }}" class="btn-primary">
                                    <i class="fas fa-arrow-up mr-2"></i>
                                    Upgrade to Seller Account
                                </a>
                            </div>
                        </div>
                    @endif
                @else
                    <!-- Login Required -->
                    <div class="max-w-2xl mx-auto text-center">
                        <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                            <i class="fas fa-sign-in-alt text-4xl text-sector-primary mb-4"></i>
                            <h2 class="text-2xl font-semibold text-sector-text mb-4">Login Required</h2>
                            <p class="text-sector-text-muted mb-6">
                                Please log in or create an account to start selling your industrial equipment.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{{ route('login') }}" class="btn-primary">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Log In
                                </a>
                                <a href="{{ route('register') }}" class="btn-secondary">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    Create Account
                                </a>
                            </div>
                        </div>
                    </div>
                @endauth
            </div>
        </div>
    </section>



    <!-- Stats Section -->
    <section class="py-16 bg-sector-card">
        <div class="max-w-6xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalListings) }}+</div>
                    <div class="text-sector-text-muted">Equipment Listings</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalBrands) }}+</div>
                    <div class="text-sector-text-muted">Trusted Brands</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalSellers) }}+</div>
                    <div class="text-sector-text-muted">Verified Sellers</div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
/* Toggle Button Styles */
.toggle-btn {
    color: #6b7280;
    background: transparent;
}

.toggle-btn.active {
    color: #ffffff;
    background: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover:not(.active) {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* Mode Content Animation */
.mode-content {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease-in-out;
}

.mode-content.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

/* Interactive World Map Styles */
.country-path {
    cursor: pointer;
    transition: all 0.2s ease;
}

.country-path:hover {
    fill: #3b82f6 !important;
    stroke: #60a5fa;
    stroke-width: 2;
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.4));
}

.country-path.selected {
    fill: #3b82f6 !important;
    stroke: #60a5fa;
    stroke-width: 2;
    filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.6));
}

.country-path.region-selected {
    fill: #1d4ed8 !important;
    stroke: #3b82f6;
}

/* Region and Industry Button Hover Effects */
.region-select-btn:hover,
.industry-select-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.region-select-btn.selected,
.industry-select-btn.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle functionality
    const buyToggle = document.getElementById('buy-toggle');
    const sellToggle = document.getElementById('sell-toggle');
    const buyMode = document.getElementById('buy-mode');
    const sellMode = document.getElementById('sell-mode');

    let selectedRegion = null;
    let currentMode = 'buy';

    // Load saved mode from session storage
    const savedMode = sessionStorage.getItem('sector360_mode');
    if (savedMode === 'sell') {
        switchToSell();
    }

    // Toggle event listeners
    buyToggle.addEventListener('click', switchToBuy);
    sellToggle.addEventListener('click', switchToSell);

    function switchToBuy() {
        currentMode = 'buy';
        sessionStorage.setItem('sector360_mode', 'buy');

        buyToggle.classList.add('active');
        sellToggle.classList.remove('active');

        sellMode.classList.add('hidden');
        setTimeout(() => {
            buyMode.classList.remove('hidden');
        }, 150);
    }

    function switchToSell() {
        currentMode = 'sell';
        sessionStorage.setItem('sector360_mode', 'sell');

        sellToggle.classList.add('active');
        buyToggle.classList.remove('active');

        buyMode.classList.add('hidden');
        setTimeout(() => {
            sellMode.classList.remove('hidden');
        }, 150);
    }

    // Interactive map functionality
    const countryPaths = document.querySelectorAll('.country-path');
    const tooltip = document.getElementById('country-tooltip');
    const tooltipCountry = document.getElementById('tooltip-country');
    const tooltipListings = document.getElementById('tooltip-listings');

    // Country to region mapping
    const countryToRegion = {
        'US': 'US',
        'CA': 'CA',
        'MX': 'US',
        'GB': 'EU',
        'DE': 'EU',
        'FR': 'EU',
        'IT': 'EU',
        'ES': 'EU',
        'RU': 'EU',
        'CN': 'ASIA',
        'JP': 'ASIA',
        'IN': 'ASIA',
        'AU': 'ASIA',
        'BR': 'US',
        'ZA': 'EU'
    };

    // Listing counts by country (mock data)
    const countryListings = {
        'US': '95,794',
        'CA': '2,736',
        'MX': '711',
        'GB': '609',
        'DE': '56',
        'FR': '310',
        'IT': '125',
        'ES': '89',
        'RU': '234',
        'CN': '445',
        'JP': '178',
        'IN': '267',
        'AU': '117',
        'BR': '892',
        'ZA': '43'
    };

    // Map interaction handlers
    countryPaths.forEach(path => {
        path.addEventListener('mouseenter', function(e) {
            const country = this.dataset.country;
            const name = this.dataset.name;
            const listings = countryListings[country] || '0';

            tooltipCountry.textContent = name;
            tooltipListings.textContent = `${listings} listings`;

            // Position tooltip
            const rect = this.getBoundingClientRect();
            const mapRect = document.getElementById('world-map-buy').getBoundingClientRect();

            tooltip.style.left = (e.clientX - mapRect.left + 10) + 'px';
            tooltip.style.top = (e.clientY - mapRect.top - 10) + 'px';
            tooltip.classList.remove('opacity-0');
        });

        path.addEventListener('mouseleave', function() {
            tooltip.classList.add('opacity-0');
        });

        path.addEventListener('mousemove', function(e) {
            const mapRect = document.getElementById('world-map-buy').getBoundingClientRect();
            tooltip.style.left = (e.clientX - mapRect.left + 10) + 'px';
            tooltip.style.top = (e.clientY - mapRect.top - 10) + 'px';
        });

        path.addEventListener('click', function() {
            const country = this.dataset.country;
            const region = countryToRegion[country];

            if (region) {
                selectedRegion = region;

                // Update map visual state
                countryPaths.forEach(p => p.classList.remove('selected', 'region-selected'));

                // Highlight selected country
                this.classList.add('selected');

                // Highlight other countries in same region
                countryPaths.forEach(p => {
                    if (countryToRegion[p.dataset.country] === region && p !== this) {
                        p.classList.add('region-selected');
                    }
                });

                // Update region buttons
                regionButtons.forEach(btn => btn.classList.remove('selected'));
                const regionButton = document.querySelector(`[data-region="${region}"]`);
                if (regionButton) {
                    regionButton.classList.add('selected');
                }

                // Show industry selection
                industrySelection.classList.remove('hidden');
                industrySelection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    });

    // Region selection functionality
    const regionButtons = document.querySelectorAll('.region-select-btn');
    const industrySelection = document.getElementById('industry-selection');
    const industryButtons = document.querySelectorAll('.industry-select-btn');

    regionButtons.forEach(button => {
        button.addEventListener('click', function() {
            selectedRegion = this.dataset.region;

            // Update button states
            regionButtons.forEach(btn => btn.classList.remove('selected'));
            this.classList.add('selected');

            // Update map visual state
            countryPaths.forEach(path => {
                path.classList.remove('selected', 'region-selected');
                if (countryToRegion[path.dataset.country] === selectedRegion) {
                    path.classList.add('region-selected');
                }
            });

            // Show industry selection
            industrySelection.classList.remove('hidden');
            industrySelection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
    });

    // Industry selection functionality
    industryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const industryId = this.dataset.industry;
            const industryName = this.dataset.name;

            // Build URL with region and industry filters
            let url = '/listings?';
            const params = [];

            if (selectedRegion) {
                params.push(`region=${selectedRegion}`);
            }
            if (industryId) {
                params.push(`industry=${industryId}`);
            }

            url += params.join('&');

            // Navigate to filtered listings
            window.location.href = url;
        });
    });


});
</script>
@endpush
@endsection
