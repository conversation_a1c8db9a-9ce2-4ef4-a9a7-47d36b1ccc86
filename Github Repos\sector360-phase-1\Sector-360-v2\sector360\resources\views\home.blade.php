@extends('layouts.app')

@section('title', 'Sector360 - The World\'s Business Marketplace')
@section('description', 'Find and sell business surplus equipment on the world\'s leading marketplace. CNC machines, medical equipment, processing machinery, and more from every sector.')

@section('content')
<div class="bg-sector-bg min-h-screen">
    <!-- Hero Section with Buy/Sell Toggle -->
    <section class="relative py-12 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-sector-text mb-4">
                    The World's Business <span class="text-sector-primary">Marketplace</span>
                </h1>
                <p class="text-lg text-sector-text-muted mb-8 max-w-2xl mx-auto">
                    Connect with buyers and sellers of business surplus equipment worldwide.
                </p>
            </div>

            <!-- Buy/Sell Toggle -->
            <div class="flex justify-center mb-8">
                <div class="bg-sector-card border border-sector-border rounded-xl p-2 inline-flex">
                    <button id="buy-toggle" class="toggle-btn active px-8 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-search mr-2"></i>
                        Buy
                    </button>
                    <button id="sell-toggle" class="toggle-btn px-8 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Sell
                    </button>
                </div>
            </div>

            <!-- Buy Mode Content -->
            <div id="buy-mode" class="mode-content">
                <!-- Interactive World Map Section -->
                <div class="bg-sector-card rounded-xl border border-sector-border p-8 mb-8">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-semibold text-sector-text mb-2">Find Equipment by Region</h2>
                        <p class="text-sector-text-muted">Select a region to explore available industrial equipment</p>
                    </div>

                    <!-- Interactive World Map Placeholder -->
                    <div id="world-map-buy" class="w-full h-80 bg-sector-bg rounded-lg border border-sector-border mb-6 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-globe-americas text-6xl text-sector-primary mb-4"></i>
                            <h3 class="text-xl font-semibold text-sector-text mb-2">Interactive World Map</h3>
                            <p class="text-sector-text-muted mb-4">Professional map integration coming soon</p>
                            <div class="text-sm text-sector-text-muted">
                                <div>🗺️ High-quality country shapes</div>
                                <div>🎯 Click-to-select regions</div>
                                <div>📊 Real-time listing data</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Region Selection -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="US">
                            <div class="font-medium text-sector-text">🇺🇸 United States</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['us_listings'] ?? '95,794' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="CA">
                            <div class="font-medium text-sector-text">🇨🇦 Canada</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['ca_listings'] ?? '2,736' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="EU">
                            <div class="font-medium text-sector-text">🇪🇺 Europe</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['eu_listings'] ?? '1,847' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="ASIA">
                            <div class="font-medium text-sector-text">🌏 Asia Pacific</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['asia_listings'] ?? '892' }} listings</div>
                        </button>
                    </div>
                </div>

                <!-- Industry Selection (Hidden by default) -->
                <div id="industry-selection" class="bg-sector-card rounded-xl border border-sector-border p-8 hidden">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-sector-text mb-2">Select Industry</h3>
                        <p class="text-sector-text-muted">Choose your industry to see relevant equipment</p>
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="industries-grid">
                        @foreach($industries as $industry)
                        <button class="industry-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-center"
                                data-industry="{{ $industry->id }}" data-name="{{ $industry->name }}">
                            <div class="w-12 h-12 bg-sector-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="{{ $industry->icon ?? 'fas fa-industry' }} text-xl text-sector-primary"></i>
                            </div>
                            <div class="font-medium text-sector-text text-sm">{{ $industry->name }}</div>
                            <div class="text-xs text-sector-text-muted">{{ number_format($industry->listings_count ?? 0) }} items</div>
                        </button>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Sell Mode Content -->
            <div id="sell-mode" class="mode-content hidden">
                @auth
                    <!-- Equipment Valuation Consultation -->
                    <div class="max-w-4xl mx-auto">
                        <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                            <div class="text-center mb-8">
                                <div class="w-16 h-16 bg-sector-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-calculator text-2xl text-sector-primary"></i>
                                </div>
                                <h2 class="text-3xl font-semibold text-sector-text mb-4">Tell us about your equipment</h2>
                                <div class="max-w-3xl mx-auto bg-sector-bg border border-sector-border rounded-lg p-4">
                                    <p class="text-sector-text-muted italic">
                                        "2018 Haas VF-4 CNC machining center, excellent condition, low hours, includes tooling and work holding. Recently serviced, all manuals included. Located in Phoenix, AZ. Upgrading to larger machine."
                                    </p>
                                </div>
                            </div>

                            <form method="POST" action="{{ route('valuations.store') }}" class="space-y-6">
                                @csrf

                                <!-- Equipment Description with Dynamic Helper -->
                                <div>
                                    <div id="description-container" class="transition-all duration-300">
                                        <div class="flex flex-col lg:flex-row gap-6">
                                            <!-- Helper Panel (hidden by default) -->
                                            <div id="helper-panel" class="hidden lg:w-1/2 bg-sector-bg border border-sector-border rounded-lg p-4">
                                                <h4 class="font-medium text-sector-text mb-3">💡 Include these details:</h4>
                                                <ul class="space-y-2 text-sm text-sector-text-muted mb-4">
                                                    <li>• Make, model, and year</li>
                                                    <li>• Current condition and running status</li>
                                                    <li>• Location and reason for sale</li>
                                                    <li>• Accessories and tooling included</li>
                                                    <li>• Recent maintenance or upgrades</li>
                                                    <li>• Any special features or modifications</li>
                                                </ul>
                                            </div>

                                            <!-- Text Area -->
                                            <div id="textarea-wrapper" class="w-full transition-all duration-300">
                                                <textarea id="equipment-description" name="description" rows="6" required
                                                          class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-4 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent text-lg transition-all duration-300"
                                                          placeholder="Tell us about your equipment..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-sm text-sector-text-muted">
                                        💡 The more details you provide, the more accurate our valuation will be
                                    </div>
                                </div>

                                <!-- Optional Price Estimate -->
                                <div>
                                    <label class="block text-sm font-medium text-sector-text mb-2">Expected value (optional)</label>
                                    <input type="number" name="estimated_value"
                                           class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                           placeholder="$0">
                                    <div class="mt-1 text-xs text-sector-text-muted">
                                        Not sure? We'll help determine fair market value
                                    </div>
                                </div>

                                <!-- Equipment Location -->
                                <div>
                                    <label class="block text-sm font-medium text-sector-text mb-2">Equipment location</label>
                                    <input type="text" name="location" required
                                           class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                           placeholder="City, State">
                                    <div class="mt-1 text-xs text-sector-text-muted">
                                        Location affects market value and shipping costs
                                    </div>
                                </div>

                                <!-- Contact Preference -->
                                <div>
                                    <label class="block text-sm font-medium text-sector-text mb-3">How would you like to receive your valuation?</label>
                                    <div class="space-y-3">
                                        <label class="flex items-center p-4 border border-sector-border rounded-lg hover:border-sector-primary cursor-pointer transition-colors">
                                            <input type="radio" name="contact_preference" value="email" checked class="text-sector-primary focus:ring-sector-primary">
                                            <div class="ml-3">
                                                <div class="font-medium text-sector-text">Email Report</div>
                                                <div class="text-sm text-sector-text-muted">Detailed PDF within 24 hours</div>
                                            </div>
                                        </label>

                                        <label class="flex items-center p-4 border border-sector-border rounded-lg hover:border-sector-primary cursor-pointer transition-colors">
                                            <input type="radio" name="contact_preference" value="phone" class="text-sector-primary focus:ring-sector-primary">
                                            <div class="ml-3">
                                                <div class="font-medium text-sector-text">Phone Consultation</div>
                                                <div class="text-sm text-sector-text-muted">15-minute expert discussion</div>
                                            </div>
                                        </label>

                                        <label class="flex items-center p-4 border border-sector-border rounded-lg hover:border-sector-primary cursor-pointer transition-colors">
                                            <input type="radio" name="contact_preference" value="data_only" class="text-sector-primary focus:ring-sector-primary">
                                            <div class="ml-3">
                                                <div class="font-medium text-sector-text">Market Data Only</div>
                                                <div class="text-sm text-sector-text-muted">Quick comparable sales info</div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Value Proposition -->
                                <div class="bg-sector-bg rounded-lg p-6 border border-sector-border">
                                    <h3 class="font-semibold text-sector-text mb-3">What you'll receive:</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div class="flex items-start">
                                            <i class="fas fa-check-circle text-sector-primary mt-0.5 mr-3"></i>
                                            <div>
                                                <div class="font-medium text-sector-text">Current market value range</div>
                                                <div class="text-sector-text-muted">Based on recent comparable sales</div>
                                            </div>
                                        </div>
                                        <div class="flex items-start">
                                            <i class="fas fa-check-circle text-sector-primary mt-0.5 mr-3"></i>
                                            <div>
                                                <div class="font-medium text-sector-text">Optimal selling strategy</div>
                                                <div class="text-sector-text-muted">Timing, pricing, and positioning advice</div>
                                            </div>
                                        </div>
                                        <div class="flex items-start">
                                            <i class="fas fa-check-circle text-sector-primary mt-0.5 mr-3"></i>
                                            <div>
                                                <div class="font-medium text-sector-text">Market trend analysis</div>
                                                <div class="text-sector-text-muted">Industry demand and pricing trends</div>
                                            </div>
                                        </div>
                                        <div class="flex items-start">
                                            <i class="fas fa-check-circle text-sector-primary mt-0.5 mr-3"></i>
                                            <div>
                                                <div class="font-medium text-sector-text">No obligation to list</div>
                                                <div class="text-sector-text-muted">Get valuation before deciding to sell</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center pt-6">
                                    <a href="{{ route('listings.create') }}" class="text-sector-primary hover:text-blue-400">
                                        Prefer traditional listing? Use full form →
                                    </a>
                                    <button type="submit" class="btn-primary px-8 py-4 text-lg">
                                        <i class="fas fa-calculator mr-2"></i>
                                        Get Expert Valuation
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                @else
                    <!-- Login Required -->
                    <div class="max-w-2xl mx-auto text-center">
                        <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                            <i class="fas fa-sign-in-alt text-4xl text-sector-primary mb-4"></i>
                            <h2 class="text-2xl font-semibold text-sector-text mb-4">Login Required</h2>
                            <p class="text-sector-text-muted mb-6">
                                Please log in or create an account to start selling your equipment.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{{ route('login') }}" class="btn-primary">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Log In
                                </a>
                                <a href="{{ route('register') }}" class="btn-secondary">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    Create Account
                                </a>
                            </div>
                        </div>
                    </div>
                @endauth
            </div>
        </div>
    </section>



    <!-- Stats Section -->
    <section class="py-16 bg-sector-card">
        <div class="max-w-6xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalListings) }}+</div>
                    <div class="text-sector-text-muted">Equipment Listings</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalBrands) }}+</div>
                    <div class="text-sector-text-muted">Trusted Brands</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalSellers) }}+</div>
                    <div class="text-sector-text-muted">Verified Sellers</div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
/* Toggle Button Styles */
.toggle-btn {
    color: #6b7280;
    background: transparent;
}

.toggle-btn.active {
    color: #ffffff;
    background: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover:not(.active) {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* Mode Content Animation */
.mode-content {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.mode-content.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

/* Map Placeholder Styles */
#world-map-buy {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Region and Industry Button Hover Effects */
.region-select-btn:hover,
.industry-select-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.region-select-btn.selected,
.industry-select-btn.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle functionality
    const buyToggle = document.getElementById('buy-toggle');
    const sellToggle = document.getElementById('sell-toggle');
    const buyMode = document.getElementById('buy-mode');
    const sellMode = document.getElementById('sell-mode');

    let selectedRegion = null;
    let currentMode = 'buy';

    // Load saved mode from session storage
    const savedMode = sessionStorage.getItem('sector360_mode');
    if (savedMode === 'sell') {
        switchToSell();
    }

    // Toggle event listeners
    buyToggle.addEventListener('click', switchToBuy);
    sellToggle.addEventListener('click', switchToSell);

    function switchToBuy() {
        currentMode = 'buy';
        sessionStorage.setItem('sector360_mode', 'buy');

        buyToggle.classList.add('active');
        sellToggle.classList.remove('active');

        sellMode.classList.add('hidden');
        buyMode.classList.remove('hidden');
    }

    function switchToSell() {
        currentMode = 'sell';
        sessionStorage.setItem('sector360_mode', 'sell');

        sellToggle.classList.add('active');
        buyToggle.classList.remove('active');

        buyMode.classList.add('hidden');
        sellMode.classList.remove('hidden');
    }

    // Map placeholder - will be replaced with professional map library

    // Dynamic helper panel for equipment description
    const equipmentDescription = document.getElementById('equipment-description');
    const helperPanel = document.getElementById('helper-panel');
    const textareaWrapper = document.getElementById('textarea-wrapper');

    if (equipmentDescription && helperPanel && textareaWrapper) {
        let helperVisible = false;

        equipmentDescription.addEventListener('focus', function() {
            if (!helperVisible) {
                // Show helper panel and adjust layout
                helperPanel.classList.remove('hidden');

                // Only adjust width on larger screens
                if (window.innerWidth >= 1024) { // lg breakpoint
                    textareaWrapper.classList.remove('w-full');
                    textareaWrapper.classList.add('lg:w-1/2');
                }

                helperVisible = true;
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (helperVisible && window.innerWidth < 1024) {
                // On mobile, keep full width
                textareaWrapper.classList.remove('lg:w-1/2');
                textareaWrapper.classList.add('w-full');
            } else if (helperVisible && window.innerWidth >= 1024) {
                // On desktop, use half width
                textareaWrapper.classList.remove('w-full');
                textareaWrapper.classList.add('lg:w-1/2');
            }
        });
    }

    // Region selection functionality
    const regionButtons = document.querySelectorAll('.region-select-btn');
    const industrySelection = document.getElementById('industry-selection');
    const industryButtons = document.querySelectorAll('.industry-select-btn');

    regionButtons.forEach(button => {
        button.addEventListener('click', function() {
            selectedRegion = this.dataset.region;

            // Update button states
            regionButtons.forEach(btn => btn.classList.remove('selected'));
            this.classList.add('selected');

            // Show industry selection
            industrySelection.classList.remove('hidden');
            industrySelection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
    });

    // Industry selection functionality
    industryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const industryId = this.dataset.industry;
            const industryName = this.dataset.name;

            // Build URL with region and industry filters
            let url = '/listings?';
            const params = [];

            if (selectedRegion) {
                params.push(`region=${selectedRegion}`);
            }
            if (industryId) {
                params.push(`industry=${industryId}`);
            }

            url += params.join('&');

            // Navigate to filtered listings
            window.location.href = url;
        });
    });


});
</script>
@endpush
@endsection
