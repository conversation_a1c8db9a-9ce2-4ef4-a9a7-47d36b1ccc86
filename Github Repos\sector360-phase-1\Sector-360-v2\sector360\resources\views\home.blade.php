@extends('layouts.app')

@section('title', 'Sector360 - The World\'s Business Marketplace')
@section('description', 'Find and sell business surplus equipment on the world\'s leading marketplace. CNC machines, medical equipment, processing machinery, and more from every sector.')

@section('content')
<div class="bg-sector-bg min-h-screen">
    <!-- Hero Section with Buy/Sell Toggle -->
    <section class="relative py-12 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-sector-text mb-4">
                    The World's Business <span class="text-sector-primary">Marketplace</span>
                </h1>
                <p class="text-lg text-sector-text-muted mb-8 max-w-2xl mx-auto">
                    Connect with buyers and sellers of business surplus equipment worldwide.
                </p>
            </div>

            <!-- Buy/Sell Toggle -->
            <div class="flex justify-center mb-8">
                <div class="bg-sector-card border border-sector-border rounded-xl p-2 inline-flex">
                    <button id="buy-toggle" class="toggle-btn active px-8 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-search mr-2"></i>
                        Buy
                    </button>
                    <button id="sell-toggle" class="toggle-btn px-8 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Sell
                    </button>
                </div>
            </div>

            <!-- Buy Mode Content -->
            <div id="buy-mode" class="mode-content">
                <!-- Interactive World Map Section -->
                <div class="bg-sector-card rounded-xl border border-sector-border p-8 mb-8">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-semibold text-sector-text mb-2">Find Equipment by Region</h2>
                        <p class="text-sector-text-muted">Select a region to explore available industrial equipment</p>
                    </div>

                    <!-- Interactive World Map Placeholder -->
                    <div id="world-map-buy" class="w-full h-80 bg-sector-bg rounded-lg border border-sector-border mb-6 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-globe-americas text-6xl text-sector-primary mb-4"></i>
                            <h3 class="text-xl font-semibold text-sector-text mb-2">Interactive World Map</h3>
                            <p class="text-sector-text-muted mb-4">Professional map integration coming soon</p>
                            <div class="text-sm text-sector-text-muted">
                                <div>🗺️ High-quality country shapes</div>
                                <div>🎯 Click-to-select regions</div>
                                <div>📊 Real-time listing data</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Region Selection -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="US">
                            <div class="font-medium text-sector-text">🇺🇸 United States</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['us_listings'] ?? '95,794' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="CA">
                            <div class="font-medium text-sector-text">🇨🇦 Canada</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['ca_listings'] ?? '2,736' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="EU">
                            <div class="font-medium text-sector-text">🇪🇺 Europe</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['eu_listings'] ?? '1,847' }} listings</div>
                        </button>
                        <button class="region-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-left" data-region="ASIA">
                            <div class="font-medium text-sector-text">🌏 Asia Pacific</div>
                            <div class="text-sm text-sector-text-muted">{{ $stats['asia_listings'] ?? '892' }} listings</div>
                        </button>
                    </div>
                </div>

                <!-- Industry Selection (Hidden by default) -->
                <div id="industry-selection" class="bg-sector-card rounded-xl border border-sector-border p-8 hidden">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-sector-text mb-2">Select Industry</h3>
                        <p class="text-sector-text-muted">Choose your industry to see relevant equipment</p>
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="industries-grid">
                        @foreach($industries as $industry)
                        <button class="industry-select-btn p-4 border border-sector-border rounded-lg hover:border-sector-primary hover:bg-sector-primary/10 transition-colors text-center"
                                data-industry="{{ $industry->id }}" data-name="{{ $industry->name }}">
                            <div class="w-12 h-12 bg-sector-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="{{ $industry->icon ?? 'fas fa-industry' }} text-xl text-sector-primary"></i>
                            </div>
                            <div class="font-medium text-sector-text text-sm">{{ $industry->name }}</div>
                            <div class="text-xs text-sector-text-muted">{{ number_format($industry->listings_count ?? 0) }} items</div>
                        </button>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Sell Mode Content -->
            <div id="sell-mode" class="mode-content hidden">
                @auth
                    @if(auth()->user()->isSeller())
                        <!-- Quick Sell Form -->
                        <div class="max-w-4xl mx-auto">
                            <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-semibold text-sector-text mb-2">List Your Equipment</h2>
                                    <p class="text-sector-text-muted">Reach thousands of buyers worldwide</p>
                                </div>

                                <form method="POST" action="{{ route('listings.store') }}" enctype="multipart/form-data" class="space-y-6">
                                    @csrf

                                    <!-- Equipment Details -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Equipment Title</label>
                                            <input type="text" name="title" required
                                                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                   placeholder="e.g., 2019 Haas VF-4 CNC Machining Center">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Price</label>
                                            <input type="number" name="price" required
                                                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                   placeholder="Enter price in USD">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Industry</label>
                                            <select name="industry_id" required class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                                                <option value="">Select Industry</option>
                                                @foreach($industries as $industry)
                                                    <option value="{{ $industry->id }}">{{ $industry->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Brand</label>
                                            <select name="brand_id" class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                                                <option value="">Select Brand</option>
                                                @foreach($brands as $brand)
                                                    <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-sector-text mb-2">Year</label>
                                            <input type="number" name="year" min="1900" max="{{ date('Y') + 1 }}"
                                                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                   placeholder="Manufacturing year">
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-sector-text mb-2">Description</label>
                                        <textarea name="description" rows="4" required
                                                  class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"
                                                  placeholder="Describe your equipment's condition, features, and specifications..."></textarea>
                                    </div>

                                    <div class="flex justify-between items-center pt-6">
                                        <a href="{{ route('listings.create') }}" class="text-sector-primary hover:text-blue-400">
                                            Need more options? Use full listing form →
                                        </a>
                                        <button type="submit" class="btn-primary px-8 py-3">
                                            <i class="fas fa-plus mr-2"></i>
                                            List Equipment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    @else
                        <!-- Upgrade to Seller -->
                        <div class="max-w-2xl mx-auto text-center">
                            <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                                <i class="fas fa-store text-4xl text-sector-primary mb-4"></i>
                                <h2 class="text-2xl font-semibold text-sector-text mb-4">Become a Seller</h2>
                                <p class="text-sector-text-muted mb-6">
                                    Upgrade your account to start selling industrial equipment and reach buyers worldwide.
                                </p>
                                <a href="{{ route('profile.edit') }}" class="btn-primary">
                                    <i class="fas fa-arrow-up mr-2"></i>
                                    Upgrade to Seller Account
                                </a>
                            </div>
                        </div>
                    @endif
                @else
                    <!-- Login Required -->
                    <div class="max-w-2xl mx-auto text-center">
                        <div class="bg-sector-card rounded-xl border border-sector-border p-8">
                            <i class="fas fa-sign-in-alt text-4xl text-sector-primary mb-4"></i>
                            <h2 class="text-2xl font-semibold text-sector-text mb-4">Login Required</h2>
                            <p class="text-sector-text-muted mb-6">
                                Please log in or create an account to start selling your industrial equipment.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{{ route('login') }}" class="btn-primary">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Log In
                                </a>
                                <a href="{{ route('register') }}" class="btn-secondary">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    Create Account
                                </a>
                            </div>
                        </div>
                    </div>
                @endauth
            </div>
        </div>
    </section>



    <!-- Stats Section -->
    <section class="py-16 bg-sector-card">
        <div class="max-w-6xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalListings) }}+</div>
                    <div class="text-sector-text-muted">Equipment Listings</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalBrands) }}+</div>
                    <div class="text-sector-text-muted">Trusted Brands</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-sector-primary">{{ number_format($totalSellers) }}+</div>
                    <div class="text-sector-text-muted">Verified Sellers</div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
/* Toggle Button Styles */
.toggle-btn {
    color: #6b7280;
    background: transparent;
}

.toggle-btn.active {
    color: #ffffff;
    background: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover:not(.active) {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* Mode Content Animation */
.mode-content {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.mode-content.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

/* Map Placeholder Styles */
#world-map-buy {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Region and Industry Button Hover Effects */
.region-select-btn:hover,
.industry-select-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.region-select-btn.selected,
.industry-select-btn.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle functionality
    const buyToggle = document.getElementById('buy-toggle');
    const sellToggle = document.getElementById('sell-toggle');
    const buyMode = document.getElementById('buy-mode');
    const sellMode = document.getElementById('sell-mode');

    let selectedRegion = null;
    let currentMode = 'buy';

    // Load saved mode from session storage
    const savedMode = sessionStorage.getItem('sector360_mode');
    if (savedMode === 'sell') {
        switchToSell();
    }

    // Toggle event listeners
    buyToggle.addEventListener('click', switchToBuy);
    sellToggle.addEventListener('click', switchToSell);

    function switchToBuy() {
        currentMode = 'buy';
        sessionStorage.setItem('sector360_mode', 'buy');

        buyToggle.classList.add('active');
        sellToggle.classList.remove('active');

        sellMode.classList.add('hidden');
        buyMode.classList.remove('hidden');
    }

    function switchToSell() {
        currentMode = 'sell';
        sessionStorage.setItem('sector360_mode', 'sell');

        sellToggle.classList.add('active');
        buyToggle.classList.remove('active');

        buyMode.classList.add('hidden');
        sellMode.classList.remove('hidden');
    }

    // Map placeholder - will be replaced with professional map library

    // Region selection functionality
    const regionButtons = document.querySelectorAll('.region-select-btn');
    const industrySelection = document.getElementById('industry-selection');
    const industryButtons = document.querySelectorAll('.industry-select-btn');

    regionButtons.forEach(button => {
        button.addEventListener('click', function() {
            selectedRegion = this.dataset.region;

            // Update button states
            regionButtons.forEach(btn => btn.classList.remove('selected'));
            this.classList.add('selected');

            // Show industry selection
            industrySelection.classList.remove('hidden');
            industrySelection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
    });

    // Industry selection functionality
    industryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const industryId = this.dataset.industry;
            const industryName = this.dataset.name;

            // Build URL with region and industry filters
            let url = '/listings?';
            const params = [];

            if (selectedRegion) {
                params.push(`region=${selectedRegion}`);
            }
            if (industryId) {
                params.push(`industry=${industryId}`);
            }

            url += params.join('&');

            // Navigate to filtered listings
            window.location.href = url;
        });
    });


});
</script>
@endpush
@endsection
