@extends('layouts.app')

@section('title', $listing->title . ' - Sector360')
@section('description', Str::limit($listing->description, 160))

@section('content')
    <!-- Breadcrumb -->
    <nav class="bg-sector-card border-b border-sector-border py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm text-sector-text-muted">
                <a href="{{ route('home') }}" class="hover:text-sector-primary">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <a href="{{ route('listings.index') }}" class="hover:text-sector-primary">Equipment</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <a href="{{ route('industries.show', $listing->industry) }}" class="hover:text-sector-primary">{{ $listing->industry->name }}</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-sector-text">{{ Str::limit($listing->title, 50) }}</span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-8 bg-sector-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column - Images and Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Image Gallery -->
                    <div class="card">
                        <div class="aspect-video bg-sector-bg rounded-lg overflow-hidden mb-4">
                            @if($listing->images->count() > 0)
                                <img src="{{ $listing->images->first()->path }}" 
                                     alt="{{ $listing->title }}"
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <i class="fas fa-image text-6xl text-sector-text-muted"></i>
                                </div>
                            @endif
                        </div>
                        
                        @if($listing->images->count() > 1)
                            <div class="grid grid-cols-4 gap-2">
                                @foreach($listing->images->take(4) as $image)
                                    <div class="aspect-square bg-sector-bg rounded overflow-hidden">
                                        <img src="{{ $image->path }}" 
                                             alt="{{ $listing->title }}"
                                             class="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity">
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Equipment Details -->
                    <div class="card">
                        <h2 class="text-xl font-bold text-sector-text mb-4">Equipment Details</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-sm font-medium text-sector-text-muted">Brand</label>
                                <p class="text-sector-text">{{ $listing->brand->name ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-sector-text-muted">Model</label>
                                <p class="text-sector-text">{{ $listing->model }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-sector-text-muted">Year</label>
                                <p class="text-sector-text">{{ $listing->year }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-sector-text-muted">Condition</label>
                                <p class="text-sector-text capitalize">{{ $listing->condition }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-sector-text-muted">Stock Number</label>
                                <p class="text-sector-text">{{ $listing->stock_number }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-sector-text-muted">Location</label>
                                <p class="text-sector-text">{{ $listing->location }}</p>
                            </div>
                            @if($listing->control_type)
                                <div>
                                    <label class="text-sm font-medium text-sector-text-muted">Control Type</label>
                                    <p class="text-sector-text">{{ $listing->control_type }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="card">
                        <h2 class="text-xl font-bold text-sector-text mb-4">Description</h2>
                        <div class="prose prose-invert max-w-none">
                            <p class="text-sector-text whitespace-pre-line">{{ $listing->description }}</p>
                        </div>
                        
                        @if($listing->additional_features)
                            <div class="mt-6">
                                <h3 class="text-lg font-semibold text-sector-text mb-2">Additional Features</h3>
                                <p class="text-sector-text">{{ $listing->additional_features }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Right Column - Seller Info and Actions -->
                <div class="space-y-6">
                    <!-- Price and Status -->
                    <div class="card">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-sector-primary mb-2">
                                {{ $listing->formatted_price }}
                            </div>
                            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                {{ $listing->status }}
                            </div>
                        </div>
                    </div>

                    <!-- Contact Seller -->
                    <div class="card">
                        <h3 class="text-lg font-bold text-sector-text mb-4">Contact Seller</h3>
                        
                        <!-- Seller Info -->
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-sector-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-sector-text">{{ $listing->user->name }}</p>
                                @if($listing->user->company_name)
                                    <p class="text-sm text-sector-text-muted">{{ $listing->user->company_name }}</p>
                                @endif
                                @if($listing->user->is_verified)
                                    <div class="flex items-center text-xs text-green-600">
                                        <i class="fas fa-shield-check mr-1"></i>
                                        Verified Seller
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Contact Actions -->
                        <div class="space-y-3">
                            @auth
                                @if(auth()->id() !== $listing->user_id)
                                    <button class="w-full btn-primary" onclick="openInquiryModal()">
                                        <i class="fas fa-envelope mr-2"></i>
                                        Send Inquiry
                                    </button>
                                    <a href="tel:{{ $listing->user->phone ?? '' }}" class="w-full btn-secondary block text-center">
                                        <i class="fas fa-phone mr-2"></i>
                                        Call Seller
                                    </a>
                                @else
                                    <a href="{{ route('listings.edit', $listing) }}" class="w-full btn-primary block text-center">
                                        <i class="fas fa-edit mr-2"></i>
                                        Edit Listing
                                    </a>
                                @endif
                            @else
                                <a href="{{ route('login') }}" class="w-full btn-primary block text-center">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Login to Contact Seller
                                </a>
                            @endauth
                        </div>
                    </div>

                    <!-- Listing Stats -->
                    <div class="card">
                        <h3 class="text-lg font-bold text-sector-text mb-4">Listing Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sector-text-muted">Listed</span>
                                <span class="text-sector-text">{{ $listing->created_at->format('M j, Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sector-text-muted">Views</span>
                                <span class="text-sector-text">{{ number_format($listing->views) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sector-text-muted">Listing ID</span>
                                <span class="text-sector-text font-mono text-sm">{{ $listing->stock_number }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Similar Listings -->
            @if($similarListings->count() > 0)
                <div class="mt-12">
                    <h2 class="text-2xl font-bold text-sector-text mb-6">Similar Equipment</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @foreach($similarListings as $similar)
                            <div class="card hover:border-sector-primary transition-all duration-200 group">
                                <div class="aspect-video bg-sector-bg rounded-lg mb-4 overflow-hidden">
                                    @if($similar->primaryImage)
                                        <img src="{{ $similar->primaryImage->path }}" 
                                             alt="{{ $similar->title }}"
                                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                                    @else
                                        <div class="w-full h-full flex items-center justify-center">
                                            <i class="fas fa-image text-2xl text-sector-text-muted"></i>
                                        </div>
                                    @endif
                                </div>
                                
                                <h3 class="font-semibold text-sector-text group-hover:text-sector-primary transition-colors line-clamp-2 mb-2">
                                    {{ $similar->title }}
                                </h3>
                                
                                <div class="flex items-center justify-between">
                                    <div class="text-lg font-bold text-sector-primary">
                                        {{ $similar->formatted_price }}
                                    </div>
                                    <a href="{{ route('listings.show', $similar) }}" class="btn-primary btn-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Inquiry Modal -->
    <div id="inquiryModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-sector-card rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-sector-text">Send Inquiry</h3>
                <button onclick="closeInquiryModal()" class="text-sector-text-muted hover:text-sector-text">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" action="{{ route('quote-requests.store') }}">
                @csrf
                <input type="hidden" name="listing_id" value="{{ $listing->id }}">

                <div class="space-y-4">
                    <div>
                        <label for="message" class="block text-sm font-medium text-sector-text mb-2">
                            Message *
                        </label>
                        <textarea id="message"
                                  name="message"
                                  required
                                  rows="4"
                                  placeholder="Hi, I'm interested in this {{ $listing->title }}. Please provide more details..."
                                  class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-3 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent"></textarea>
                    </div>


                </div>

                <div class="flex space-x-3 mt-6">
                    <button type="button" onclick="closeInquiryModal()" class="flex-1 btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 btn-primary">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Inquiry
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function openInquiryModal() {
    document.getElementById('inquiryModal').classList.remove('hidden');
}

function closeInquiryModal() {
    document.getElementById('inquiryModal').classList.add('hidden');
}
</script>
@endpush
