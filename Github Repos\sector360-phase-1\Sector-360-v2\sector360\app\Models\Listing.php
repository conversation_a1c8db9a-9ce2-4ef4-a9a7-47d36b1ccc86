<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Listing extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'user_id', 'title', 'slug', 'brand_id', 'model', 'year', 'condition',
        'price', 'currency', 'location', 'description', 'specifications',
        'specifications_metric', 'stock_number', 'industry_id', 'category_id',
        'equipment_type_id', 'group_id', 'status', 'control_type',
        'additional_features', 'featured'
    ];

    protected $casts = [
        'specifications' => 'array',
        'specifications_metric' => 'array',
        'featured' => 'boolean',
        'year' => 'integer',
        'price' => 'decimal:2',
        'views' => 'integer',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom(['title', 'stock_number'])
            ->saveSlugsTo('slug');
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function industry()
    {
        return $this->belongsTo(Industry::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function equipmentType()
    {
        return $this->belongsTo(EquipmentType::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function images()
    {
        return $this->hasMany(ListingImage::class)->orderBy('sort_order');
    }

    public function primaryImage()
    {
        return $this->hasOne(ListingImage::class)->where('is_primary', true);
    }

    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        if (!$this->price) {
            return 'Contact for Price';
        }

        return '$' . number_format($this->price, 0);
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    // Scopes
    public function scopeForSale($query)
    {
        return $query->where('status', 'For Sale');
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Methods
    public function incrementViews()
    {
        $this->increment('views');
    }
}
