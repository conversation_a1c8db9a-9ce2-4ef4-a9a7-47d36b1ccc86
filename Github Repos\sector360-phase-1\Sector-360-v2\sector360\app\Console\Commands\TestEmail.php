<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\WelcomeUser;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email configuration by sending a test email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        try {
            // Create a temporary user for testing
            $testUser = new User([
                'name' => 'Test User',
                'email' => $email,
                'user_type' => 'buyer',
                'company_name' => 'Test Company'
            ]);

            // Send welcome notification
            $testUser->notify(new WelcomeUser());

            $this->info("Test email sent successfully to: {$email}");
            $this->info("Check your email inbox and spam folder.");

        } catch (\Exception $e) {
            $this->error("Failed to send email: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
