<?php

namespace App\Http\Controllers;

use App\Models\QuoteRequest;
use App\Models\Listing;
use App\Notifications\QuoteRequestReceived;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class QuoteRequestController extends Controller
{
    /**
     * Store a new quote request
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'listing_id' => 'required|exists:listings,id',
            'message' => 'required|string|min:10|max:1000',
        ]);

        $listing = Listing::findOrFail($validated['listing_id']);

        // Prevent users from requesting quotes on their own listings
        if (Auth::id() === $listing->user_id) {
            return back()->with('error', 'You cannot request a quote on your own listing.');
        }

        $validated['user_id'] = Auth::id();
        $validated['seller_id'] = $listing->user_id;
        $validated['status'] = 'pending';

        $quoteRequest = QuoteRequest::create($validated);

        // Send to N8N webhook
        $this->sendToN8N($quoteRequest, $listing);

        // Send email notification to seller
        $listing->user->notify(new QuoteRequestReceived($quoteRequest));

        // Return JSON response for AJAX requests
        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Your inquiry has been sent to the seller! They will receive an email notification.'
            ]);
        }

        return back()->with('success', 'Your inquiry has been sent to the seller! They will receive an email notification.');
    }

    /**
     * Show quote requests for authenticated user
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isSeller()) {
            // Show received quote requests
            $quoteRequests = QuoteRequest::where('seller_id', $user->id)
                ->with(['user', 'listing'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);
        } else {
            // Show sent quote requests
            $quoteRequests = QuoteRequest::where('user_id', $user->id)
                ->with(['seller', 'listing'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);
        }

        return view('quote-requests.index', compact('quoteRequests'));
    }

    /**
     * Update quote request status
     */
    public function update(Request $request, QuoteRequest $quoteRequest)
    {
        // Only the seller can update the status
        if (Auth::id() !== $quoteRequest->seller_id) {
            abort(403);
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,responded,closed',
            'response' => 'nullable|string|max:1000',
        ]);

        $quoteRequest->update($validated);

        return back()->with('success', 'Quote request updated successfully!');
    }

    /**
     * Send inquiry data to N8N webhook
     */
    private function sendToN8N(QuoteRequest $quoteRequest, Listing $listing)
    {
        try {
            $webhookUrl = 'https://oakhill007.app.n8n.cloud/webhook/4cdafc11-f6a1-493f-aa83-1ea7f62bb43a';

            $payload = [
                'listing_id' => $listing->id,
                'listing_title' => $listing->title,
                'listing_price' => $listing->price,
                'listing_location' => $listing->location,
                'listing_url' => route('listings.show', $listing),
                'listing_stock_number' => $listing->stock_number,
                'listing_brand' => $listing->brand->name ?? null,
                'listing_industry' => $listing->industry->name ?? null,
                'seller_id' => $listing->user_id,
                'seller_name' => $listing->user->name,
                'seller_email' => $listing->user->email,
                'seller_company' => $listing->user->company_name,
                'buyer_id' => $quoteRequest->user_id,
                'buyer_name' => $quoteRequest->user->name,
                'buyer_email' => $quoteRequest->user->email,
                'buyer_company' => $quoteRequest->user->company_name,
                'message' => $quoteRequest->message,
                'quote_request_id' => $quoteRequest->id,
                'timestamp' => $quoteRequest->created_at->toISOString(),
                'source' => 'sector360',
                'environment' => config('app.env')
            ];

            $response = Http::timeout(10)->post($webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('N8N webhook sent successfully', [
                    'quote_request_id' => $quoteRequest->id,
                    'listing_id' => $listing->id,
                    'response_status' => $response->status()
                ]);
            } else {
                Log::warning('N8N webhook failed', [
                    'quote_request_id' => $quoteRequest->id,
                    'listing_id' => $listing->id,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('N8N webhook exception', [
                'quote_request_id' => $quoteRequest->id,
                'listing_id' => $listing->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
