<?php

namespace App\Http\Controllers;

use App\Models\QuoteRequest;
use App\Models\Listing;
use App\Notifications\QuoteRequestReceived;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QuoteRequestController extends Controller
{
    /**
     * Store a new quote request
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'listing_id' => 'required|exists:listings,id',
            'message' => 'required|string|min:10|max:1000',
        ]);

        $listing = Listing::findOrFail($validated['listing_id']);

        // Prevent users from requesting quotes on their own listings
        if (Auth::id() === $listing->user_id) {
            return back()->with('error', 'You cannot request a quote on your own listing.');
        }

        $validated['user_id'] = Auth::id();
        $validated['seller_id'] = $listing->user_id;
        $validated['status'] = 'pending';

        $quoteRequest = QuoteRequest::create($validated);

        // Send email notification to seller
        $listing->user->notify(new QuoteRequestReceived($quoteRequest));

        return back()->with('success', 'Your inquiry has been sent to the seller! They will receive an email notification.');
    }

    /**
     * Show quote requests for authenticated user
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isSeller()) {
            // Show received quote requests
            $quoteRequests = QuoteRequest::where('seller_id', $user->id)
                ->with(['user', 'listing'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);
        } else {
            // Show sent quote requests
            $quoteRequests = QuoteRequest::where('user_id', $user->id)
                ->with(['seller', 'listing'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);
        }

        return view('quote-requests.index', compact('quoteRequests'));
    }

    /**
     * Update quote request status
     */
    public function update(Request $request, QuoteRequest $quoteRequest)
    {
        // Only the seller can update the status
        if (Auth::id() !== $quoteRequest->seller_id) {
            abort(403);
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,responded,closed',
            'response' => 'nullable|string|max:1000',
        ]);

        $quoteRequest->update($validated);

        return back()->with('success', 'Quote request updated successfully!');
    }
}
