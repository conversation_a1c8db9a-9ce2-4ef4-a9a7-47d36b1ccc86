APP_NAME="Sector360"
APP_ENV=local
APP_KEY=base64:Fb+0d05+OImxq8v9cooBGBKRRog364knuxvSX5WvoiU=
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=db.mfskapjccvaenizkgobg.supabase.co
DB_PORT=5432
DB_DATABASE=postgres
DB_USERNAME=postgres
DB_PASSWORD=Topher@supa1991

# Supabase Configuration
SUPABASE_URL=https://mfskapjccvaenizkgobg.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1mc2thcGpjY3ZhZW5pemtnb2JnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjkwMDUsImV4cCI6MjA2NTU0NTAwNX0.9PL8etU-UiCXzdbH3XbV17REgDO3ln7pplAiLwQit3w

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=bppuxxiwoqmabrxm
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Sector360"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Google OAuth
GOOGLE_CLIENT_ID=659726318139-gikfp0l1b7dkjnbvpntr52oqdk2o9k6h.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# SSL Certificate for cURL (disabled SSL verification for development)
# CURL_CA_BUNDLE=cacert.pem
