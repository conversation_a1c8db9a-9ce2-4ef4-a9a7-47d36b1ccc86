<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('listings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title', 200);
            $table->string('slug')->unique();
            $table->foreignId('brand_id')->nullable()->constrained();
            $table->string('model', 100)->nullable();
            $table->year('year')->nullable();
            $table->enum('condition', ['new', 'used']);
            $table->decimal('price', 12, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->string('location', 100);
            $table->text('description');
            $table->json('specifications')->nullable(); // Stores all spec fields
            $table->json('specifications_metric')->nullable(); // Metric conversions
            $table->string('stock_number', 50)->unique();
            $table->foreignId('industry_id')->constrained();
            $table->foreignId('category_id')->constrained();
            $table->foreignId('equipment_type_id')->nullable()->constrained();
            $table->foreignId('group_id')->nullable()->constrained();
            $table->enum('status', ['For Sale', 'Sold', 'Pending'])->default('For Sale');
            $table->string('control_type')->nullable(); // e.g., "Fanuc 16T / MSC516 Control"
            $table->text('additional_features')->nullable(); // e.g., "BB500/230 Air Chucks, Chip Conveyor"
            $table->unsignedInteger('views')->default(0);
            $table->boolean('featured')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['industry_id', 'category_id', 'equipment_type_id']);
            $table->index(['brand_id', 'model']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('listings');
    }
};
