<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('company_name', 100)->nullable()->after('name');
            $table->string('phone', 20)->nullable();
            $table->string('location', 100)->nullable();
            $table->text('address')->nullable();
            $table->enum('user_type', ['buyer', 'seller', 'admin'])->default('buyer');
            $table->boolean('is_verified')->default(false);
            $table->timestamp('last_login_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'company_name', 'phone', 'location', 'address',
                'user_type', 'is_verified', 'last_login_at'
            ]);
        });
    }
};
