<?php

namespace App\Http\Controllers;

use App\Models\Industry;
use App\Models\Brand;
use App\Models\Listing;
use App\Models\User;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get real statistics but enhance with realistic mock data for demo
        $realListings = Listing::forSale()->count();
        $realBrands = Brand::count();
        $realSellers = User::sellers()->verified()->count();

        // Enhanced statistics with realistic marketplace numbers
        $totalListings = max($realListings, 0) + 127589; // Base of ~127k listings
        $totalBrands = max($realBrands, 0) + 2847; // Base of ~2.8k brands
        $totalSellers = max($realSellers, 0) + 18934; // Base of ~18k verified sellers

        // Get all industries with listing counts for the interactive map
        $industries = Industry::withCount('listings')->get();

        // Get all brands for the sell form
        $brands = Brand::orderBy('name')->get();

        // Regional statistics (realistic mock data for demo)
        $stats = [
            'us_listings' => '95,794',
            'ca_listings' => '2,736',
            'eu_listings' => '1,847',
            'asia_listings' => '892'
        ];

        return view('home', compact(
            'totalListings',
            'totalBrands',
            'totalSellers',
            'industries',
            'brands',
            'stats'
        ));
    }
}
