<?php

namespace App\Http\Controllers;

use App\Models\Industry;
use App\Models\Brand;
use App\Models\Listing;
use App\Models\User;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get statistics for the hero section
        $totalListings = Listing::forSale()->count();
        $totalBrands = Brand::count();
        $totalSellers = User::sellers()->verified()->count();

        // Get featured content
        $industries = Industry::active()->ordered()->take(8)->get();
        $featuredListings = Listing::forSale()
            ->featured()
            ->with(['brand', 'industry', 'primaryImage', 'user'])
            ->take(6)
            ->get();

        $popularBrands = Brand::featured()
            ->withCount('listings')
            ->orderBy('listings_count', 'desc')
            ->take(8)
            ->get();

        return view('home', compact(
            'totalListings',
            'totalBrands',
            'totalSellers',
            'industries',
            'featuredListings',
            'popularBrands'
        ));
    }
}
