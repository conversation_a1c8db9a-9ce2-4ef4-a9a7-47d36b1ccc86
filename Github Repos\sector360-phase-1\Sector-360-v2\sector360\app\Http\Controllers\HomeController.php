<?php

namespace App\Http\Controllers;

use App\Models\Industry;
use App\Models\Brand;
use App\Models\Listing;
use App\Models\User;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get statistics for the hero section
        $totalListings = Listing::forSale()->count();
        $totalBrands = Brand::count();
        $totalSellers = User::sellers()->verified()->count();

        // Get all industries with listing counts for the interactive map
        $industries = Industry::withCount('listings')->get();

        // Get all brands for the sell form
        $brands = Brand::orderBy('name')->get();

        // Regional statistics (mock data for demo)
        $stats = [
            'us_listings' => '95,794',
            'ca_listings' => '2,736',
            'eu_listings' => '1,847',
            'asia_listings' => '892'
        ];

        return view('home', compact(
            'totalListings',
            'totalBrands',
            'totalSellers',
            'industries',
            'brands',
            'stats'
        ));
    }
}
