# Sector360 Marketplace Demo - Part 5: Controllers & Routes

## Controllers

### HomeController

```php
<?php
// app/Http/Controllers/HomeController.php
namespace App\Http\Controllers;

use App\Models\Listing;
use App\Models\Industry;
use App\Models\Brand;
use App\Models\User;

class HomeController extends Controller
{
    public function index()
    {
        $totalListings = Listing::active()->count();
        $totalBrands = Brand::count();
        $totalSellers = User::where('user_type', 'seller')->count();
        
        $industries = Industry::active()
            ->withCount(['listings' => function ($query) {
                $query->active();
            }])
            ->orderBy('sort_order')
            ->get();
        
        $featuredListings = Listing::active()
            ->featured()
            ->with(['images', 'equipmentType', 'user'])
            ->take(8)
            ->get();
        
        $popularBrands = Brand::withCount(['listings' => function ($query) {
                $query->active();
            }])
            ->having('listings_count', '>', 0)
            ->orderBy('listings_count', 'desc')
            ->take(12)
            ->get();
        
        return view('home', compact(
            'totalListings',
            'totalBrands', 
            'totalSellers',
            'industries',
            'featuredListings',
            'popularBrands'
        ));
    }
}
```

### ListingController (Excerpt)

```php
<?php
// app/Http/Controllers/ListingController.php
namespace App\Http\Controllers;

use App\Models\Listing;
use App\Models\Industry;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ListingController extends Controller
{
    public function index(Request $request)
    {
        return $this->search($request);
    }

    public function search(Request $request)
    {
        $query = Listing::active()
            ->with(['images', 'brand', 'equipmentType', 'user']);

        // Search query
        if ($request->filled('q')) {
            $searchTerm = $request->q;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('model', 'like', "%{$searchTerm}%")
                  ->orWhereHas('brand', function ($q) use ($searchTerm) {
                      $q->where('name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Apply filters
        $this->applyFilters($query, $request);

        // Sorting
        $this->applySorting($query, $request->get('sort', 'newest'));

        $listings = $query->paginate(24);

        // Get filter data
        $countries = $this->getCountryFilters();
        $industries = $this->getIndustryFilters();

        return view('listings.search', compact('listings', 'countries', 'industries'));
    }

    public function show(Listing $listing)
    {
        abort_if($listing->status !== 'active', 404);

        $listing->load(['images', 'brand', 'equipmentType', 'industry', 'category', 'user']);
        $listing->increment('views');

        $similarListings = $this->getSimilarListings($listing);

        return view('listings.show', compact('listing', 'similarListings'));
    }

    public function create()
    {
        $this->authorize('create', Listing::class);
        
        $industries = Industry::active()->get();
        $brands = Brand::orderBy('name')->get();
        
        return view('listings.create', compact('industries', 'brands'));
    }

    public function store(Request $request)
    {
        $this->authorize('create', Listing::class);

        $validated = $this->validateListing($request);

        $listing = DB::transaction(function () use ($validated, $request) {
            $listing = auth()->user()->listings()->create($validated);
            
            if ($request->hasFile('images')) {
                $this->handleImageUploads($listing, $request->file('images'));
            }
            
            return $listing;
        });

        return redirect()->route('listings.show', $listing)
            ->with('success', 'Listing created successfully!');
    }

    protected function applyFilters($query, Request $request)
    {
        if ($request->filled('location')) {
            $query->whereIn('country', $request->location);
        }

        if ($request->filled('industry')) {
            $query->whereIn('industry_id', $request->industry);
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        if ($request->filled('condition')) {
            $query->whereIn('condition', $request->condition);
        }
    }

    protected function applySorting($query, $sort)
    {
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'year_new':
                $query->orderBy('year', 'desc');
                break;
            default:
                $query->latest();
        }
    }
}
```

### QuoteRequestController

```php
<?php
// app/Http/Controllers/QuoteRequestController.php
namespace App\Http\Controllers;

use App\Models\Listing;
use App\Models\QuoteRequest;
use Illuminate\Http\Request;
use App\Notifications\NewQuoteRequest;

class QuoteRequestController extends Controller
{
    public function store(Request $request, Listing $listing)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:100',
            'message' => 'required|string|max:1000',
        ]);

        $quoteRequest = $listing->quoteRequests()->create([
            'user_id' => auth()->id(),
            'requester_name' => $validated['name'],
            'requester_email' => $validated['email'],
            'requester_phone' => $validated['phone'] ?? null,
            'requester_company' => $validated['company'] ?? null,
            'message' => $validated['message'],
            'status' => 'pending',
        ]);

        // Notify the seller
        $listing->user->notify(new NewQuoteRequest($quoteRequest));

        return response()->json([
            'success' => true,
            'message' => 'Quote request sent successfully!'
        ]);
    }

    public function index()
    {
        $this->authorize('viewAny', QuoteRequest::class);

        $quoteRequests = auth()->user()->receivedQuoteRequests()
            ->with(['listing.images', 'user'])
            ->latest()
            ->paginate(20);

        return view('quote-requests.index', compact('quoteRequests'));
    }

    public function show(QuoteRequest $quoteRequest)
    {
        $this->authorize('view', $quoteRequest);

        $quoteRequest->load(['listing.images', 'user']);

        if (!$quoteRequest->is_read) {
            $quoteRequest->markAsRead();
        }

        return view('quote-requests.show', compact('quoteRequest'));
    }
}
```

### BrowseController

```php
<?php
// app/Http/Controllers/BrowseController.php
namespace App\Http\Controllers;

use App\Models\Industry;
use App\Models\Brand;
use App\Models\Category;

class BrowseController extends Controller
{
    public function industries()
    {
        $industries = Industry::active()
            ->withCount(['listings' => function ($query) {
                $query->active();
            }])
            ->orderBy('sort_order')
            ->get();

        return view('browse.industries', compact('industries'));
    }

    public function industry(Industry $industry)
    {
        $groups = $industry->groups()
            ->with(['categories' => function ($query) {
                $query->withCount(['listings' => function ($q) {
                    $q->active();
                }]);
            }])
            ->get();

        return view('browse.industry', compact('industry', 'groups'));
    }

    public function brands()
    {
        $brands = Brand::withCount(['listings' => function ($query) {
                $query->active();
            }])
            ->having('listings_count', '>', 0)
            ->orderBy('name')
            ->paginate(48);

        return view('browse.brands', compact('brands'));
    }

    public function brand(Brand $brand)
    {
        $listings = $brand->listings()
            ->active()
            ->with(['images', 'equipmentType', 'user'])
            ->paginate(24);

        return view('browse.brand', compact('brand', 'listings'));
    }
}
```

## Routes

```php
<?php
// routes/web.php
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ListingController;
use App\Http\Controllers\QuoteRequestController;
use App\Http\Controllers\BrowseController;
use App\Http\Controllers\ProfileController;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Listing Routes
Route::get('/listings', [ListingController::class, 'index'])->name('listings.index');
Route::get('/listings/search', [ListingController::class, 'search'])->name('listings.search');
Route::get('/listings/{listing}', [ListingController::class, 'show'])->name('listings.show');

// Browse Routes
Route::get('/industries', [BrowseController::class, 'industries'])->name('industries.index');
Route::get('/industries/{industry:slug}', [BrowseController::class, 'industry'])->name('industries.show');
Route::get('/brands', [BrowseController::class, 'brands'])->name('brands.index');
Route::get('/brands/{brand:slug}', [BrowseController::class, 'brand'])->name('brands.show');
Route::get('/categories/{category:slug}', [BrowseController::class, 'category'])->name('categories.show');

// Authentication Required Routes
Route::middleware(['auth'])->group(function () {
    // Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    
    // Seller Routes
    Route::middleware(['role:seller'])->group(function () {
        Route::get('/listings/create', [ListingController::class, 'create'])->name('listings.create');
        Route::post('/listings', [ListingController::class, 'store'])->name('listings.store');
        Route::get('/listings/{listing}/edit', [ListingController::class, 'edit'])->name('listings.edit');
        Route::put('/listings/{listing}', [ListingController::class, 'update'])->name('listings.update');
        Route::delete('/listings/{listing}', [ListingController::class, 'destroy'])->name('listings.destroy');
        
        Route::get('/my-listings', [ListingController::class, 'myListings'])->name('listings.my');
        Route::get('/quote-requests', [QuoteRequestController::class, 'index'])->name('quote-requests.index');
        Route::get('/quote-requests/{quoteRequest}', [QuoteRequestController::class, 'show'])->name('quote-requests.show');
    });
    
    // Quote Requests (any authenticated user)
    Route::post('/listings/{listing}/quote', [QuoteRequestController::class, 'store'])->name('quote-requests.store');
});

// Admin Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::resource('users', AdminUserController::class);
    Route::resource('listings', AdminListingController::class);
    Route::patch('/listings/{listing}/approve', [AdminListingController::class, 'approve'])->name('listings.approve');
});

require __DIR__.'/auth.php';
```
