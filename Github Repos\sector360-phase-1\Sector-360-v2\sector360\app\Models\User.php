<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasA<PERSON>Tokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'company_name',
        'phone',
        'location',
        'address',
        'user_type',
        'is_verified',
        'last_login_at',
        'preferred_countries',
        'preferred_industry_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_verified' => 'boolean',
        'last_login_at' => 'datetime',
        'preferred_countries' => 'array',
    ];

    // Relationships
    public function listings()
    {
        return $this->hasMany(Listing::class);
    }

    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    public function savedSearches()
    {
        return $this->hasMany(SavedSearch::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    // User type methods - simplified like eBay
    public function isSeller()
    {
        // Everyone can sell (like eBay) - only admins are special
        return $this->user_type !== 'admin';
    }

    public function isBuyer()
    {
        // Everyone can buy
        return true;
    }

    public function isAdmin()
    {
        return $this->user_type === 'admin';
    }

    // Scopes
    public function scopeSellers($query)
    {
        return $query->where('user_type', 'seller');
    }

    public function scopeBuyers($query)
    {
        return $query->where('user_type', 'buyer');
    }

    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }
}
