<nav x-data="{ open: false, searchOpen: false }" class="bg-sector-card border-b border-sector-border sticky top-0 z-50">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('home') }}" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-sector-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-industry text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold text-sector-text">Sector360</span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-8 sm:ml-10 sm:flex">
                    <a href="{{ route('listings.index') }}" class="text-sector-text hover:text-sector-primary px-3 py-2 text-sm font-medium transition-colors">
                        Browse Equipment
                    </a>
                    <a href="{{ route('industries.index') }}" class="text-sector-text hover:text-sector-primary px-3 py-2 text-sm font-medium transition-colors">
                        Industries
                    </a>
                    <a href="{{ route('brands.index') }}" class="text-sector-text hover:text-sector-primary px-3 py-2 text-sm font-medium transition-colors">
                        Brands
                    </a>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="hidden md:flex flex-1 max-w-lg mx-8">
                <div class="relative w-full">
                    <input type="text"
                           placeholder="Search equipment..."
                           class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-2 pl-10 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-sector-text-muted"></i>
                </div>
            </div>

            <!-- Right Side Navigation -->
            <div class="flex items-center space-x-4">
                <!-- Mobile Search Toggle -->
                <button @click="searchOpen = !searchOpen" class="md:hidden text-sector-text hover:text-sector-primary">
                    <i class="fas fa-search"></i>
                </button>

                @auth
                    <!-- Sell Equipment Button -->
                    @if(auth()->user()->isSeller() || auth()->user()->isAdmin())
                        <a href="{{ route('listings.create') }}" class="btn-primary hidden sm:inline-flex">
                            <i class="fas fa-plus mr-2"></i>
                            Sell Equipment
                        </a>
                    @endif

                    <!-- User Dropdown -->
                    <x-dropdown align="right" width="48">
                        <x-slot name="trigger">
                            <button class="flex items-center space-x-2 text-sector-text hover:text-sector-primary transition-colors">
                                <div class="w-8 h-8 bg-sector-primary rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </span>
                                </div>
                                <span class="hidden sm:block">{{ Auth::user()->name }}</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                        </x-slot>

                        <x-slot name="content">
                            <div class="px-4 py-2 border-b border-sector-border">
                                <div class="text-sm font-medium text-sector-text">{{ Auth::user()->name }}</div>
                                <div class="text-xs text-sector-text-muted">{{ Auth::user()->email }}</div>
                                @if(Auth::user()->company_name)
                                    <div class="text-xs text-sector-text-muted">{{ Auth::user()->company_name }}</div>
                                @endif
                            </div>

                            <x-dropdown-link :href="route('dashboard')">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </x-dropdown-link>

                            <x-dropdown-link :href="route('profile.edit')">
                                <i class="fas fa-user mr-2"></i>
                                Profile
                            </x-dropdown-link>

                            @if(auth()->user()->isSeller() || auth()->user()->isAdmin())
                                <x-dropdown-link :href="route('listings.my')">
                                    <i class="fas fa-list mr-2"></i>
                                    My Listings
                                </x-dropdown-link>
                            @endif

                            <x-dropdown-link :href="route('favorites.index')">
                                <i class="fas fa-heart mr-2"></i>
                                Favorites
                            </x-dropdown-link>

                            <div class="border-t border-sector-border"></div>

                            <!-- Authentication -->
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <x-dropdown-link :href="route('logout')"
                                        onclick="event.preventDefault(); this.closest('form').submit();">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    Log Out
                                </x-dropdown-link>
                            </form>
                        </x-slot>
                    </x-dropdown>
                @else
                    <!-- Guest Navigation -->
                    <a href="{{ route('login') }}" class="text-sector-text hover:text-sector-primary transition-colors">
                        Log In
                    </a>
                    <a href="{{ route('register') }}" class="btn-primary">
                        Sign Up
                    </a>
                @endauth
            </div>

            <!-- Hamburger -->
            <div class="flex items-center sm:hidden">
                <button @click="open = ! open" class="text-sector-text hover:text-sector-primary p-2">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div x-show="searchOpen" x-transition class="md:hidden px-4 py-3 border-t border-sector-border">
        <div class="relative">
            <input type="text"
                   placeholder="Search equipment..."
                   class="w-full bg-sector-bg border border-sector-border rounded-lg px-4 py-2 pl-10 text-sector-text placeholder-sector-text-muted focus:ring-2 focus:ring-sector-primary focus:border-transparent">
            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-sector-text-muted"></i>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden border-t border-sector-border">
        <div class="pt-2 pb-3 space-y-1 px-4">
            <a href="{{ route('listings.index') }}" class="block text-sector-text hover:text-sector-primary py-2 text-base font-medium">
                Browse Equipment
            </a>
            <a href="{{ route('industries.index') }}" class="block text-sector-text hover:text-sector-primary py-2 text-base font-medium">
                Industries
            </a>
            <a href="{{ route('brands.index') }}" class="block text-sector-text hover:text-sector-primary py-2 text-base font-medium">
                Brands
            </a>
        </div>

        @auth
            <!-- Responsive Settings Options -->
            <div class="pt-4 pb-1 border-t border-sector-border">
                <div class="px-4">
                    <div class="font-medium text-base text-sector-text">{{ Auth::user()->name }}</div>
                    <div class="font-medium text-sm text-sector-text-muted">{{ Auth::user()->email }}</div>
                    @if(Auth::user()->company_name)
                        <div class="font-medium text-sm text-sector-text-muted">{{ Auth::user()->company_name }}</div>
                    @endif
                </div>

                <div class="mt-3 space-y-1 px-4">
                    @if(auth()->user()->isSeller() || auth()->user()->isAdmin())
                        <a href="{{ route('listings.create') }}" class="block btn-primary text-center mb-3">
                            <i class="fas fa-plus mr-2"></i>
                            Sell Equipment
                        </a>
                    @endif

                    <x-responsive-nav-link :href="route('dashboard')">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Dashboard
                    </x-responsive-nav-link>

                    <x-responsive-nav-link :href="route('profile.edit')">
                        <i class="fas fa-user mr-2"></i>
                        Profile
                    </x-responsive-nav-link>

                    @if(auth()->user()->isSeller() || auth()->user()->isAdmin())
                        <x-responsive-nav-link :href="route('listings.my')">
                            <i class="fas fa-list mr-2"></i>
                            My Listings
                        </x-responsive-nav-link>
                    @endif

                    <x-responsive-nav-link :href="route('favorites.index')">
                        <i class="fas fa-heart mr-2"></i>
                        Favorites
                    </x-responsive-nav-link>

                    <!-- Authentication -->
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <x-responsive-nav-link :href="route('logout')"
                                onclick="event.preventDefault(); this.closest('form').submit();">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Log Out
                        </x-responsive-nav-link>
                    </form>
                </div>
            </div>
        @else
            <!-- Guest Mobile Menu -->
            <div class="pt-4 pb-1 border-t border-sector-border px-4 space-y-2">
                <a href="{{ route('login') }}" class="block btn-secondary text-center">
                    Log In
                </a>
                <a href="{{ route('register') }}" class="block btn-primary text-center">
                    Sign Up
                </a>
            </div>
        @endauth
    </div>
</nav>
