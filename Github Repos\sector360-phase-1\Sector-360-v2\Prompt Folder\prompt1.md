# Sector360 Marketplace Demo - Part 1: Foundation & Database

## Project Overview

Build a fully functional Laravel PHP demo marketplace called **Sector360** ("The World's Business Marketplace") that closely mirrors the functionality and design of equipt.com, an industrial equipment marketplace. The demo must be production-ready, bug-free, and showcase a modern dark-themed UI with complete functionality including user authentication, listing management, search/filtering, and quote requests.

### Key Requirements:
- **Framework**: Laravel 10.x with Blade templates
- **Styling**: Tailwind CSS 3.x with dark theme
- **Database**: MySQL 8.0
- **Authentication**: <PERSON><PERSON> (email/password only, no social login)
- **Timeline**: Demo-ready within 1 week
- **Sample Data**: Realistic industrial equipment listings with images

### User Roles:
1. **Buyers**: Browse, search, filter listings, request quotes, save searches
2. **Sellers**: Create/manage listings, receive quote requests, upload images
3. **Admins**: Full access to manage users, listings, categories, and settings

## Technical Stack & Dependencies

```bash
# Laravel Installation
composer create-project laravel/laravel sector360 "10.*"
cd sector360

# Required Packages
composer require laravel/breeze --dev
composer require intervention/image "^2.7"
composer require laravel/scout "^10.0"
composer require spatie/laravel-permission "^6.0"
composer require spatie/laravel-sluggable "^3.5"
composer require spatie/laravel-medialibrary "^11.0"

# Install Breeze with Blade
php artisan breeze:install blade
npm install
npm run build

# Additional NPM packages
npm install alpinejs @alpinejs/persist
npm install @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio
```

## Database Schema

### Core Tables Structure

```sql
-- Industries table
Schema::create('industries', function (Blueprint $table) {
    $table->id();
    $table->string('name', 100);
    $table->string('slug', 100)->unique();
    $table->string('icon_class', 50)->nullable(); // Font Awesome class
    $table->text('description')->nullable();
    $table->unsignedInteger('sort_order')->default(0);
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});

-- Groups table (for breadcrumb hierarchy)
Schema::create('groups', function (Blueprint $table) {
    $table->id();
    $table->foreignId('industry_id')->constrained()->onDelete('cascade');
    $table->string('name', 100);
    $table->string('slug', 100);
    $table->unsignedInteger('sort_order')->default(0);
    $table->timestamps();
    
    $table->unique(['industry_id', 'slug']);
});

-- Categories table
Schema::create('categories', function (Blueprint $table) {
    $table->id();
    $table->foreignId('industry_id')->constrained()->onDelete('cascade');
    $table->foreignId('group_id')->nullable()->constrained()->onDelete('cascade');
    $table->string('name', 100);
    $table->string('slug', 100);
    $table->text('description')->nullable();
    $table->unsignedInteger('sort_order')->default(0);
    $table->boolean('is_active')->default(true);
    $table->timestamps();
    
    $table->unique(['industry_id', 'slug']);
});

-- Equipment types table
Schema::create('equipment_types', function (Blueprint $table) {
    $table->id();
    $table->foreignId('category_id')->constrained()->onDelete('cascade');
    $table->string('name', 100);
    $table->string('slug', 100);
    $table->timestamps();
    
    $table->unique(['category_id', 'slug']);
});

-- Brands table
Schema::create('brands', function (Blueprint $table) {
    $table->id();
    $table->string('name', 100);
    $table->string('slug', 100)->unique();
    $table->string('logo_url')->nullable();
    $table->boolean('is_featured')->default(false);
    $table->timestamps();
});

-- Users table modifications (add after Laravel's default users migration)
Schema::table('users', function (Blueprint $table) {
    $table->string('company_name', 100)->nullable()->after('name');
    $table->string('phone', 20)->nullable();
    $table->string('location', 100)->nullable();
    $table->text('address')->nullable();
    $table->enum('user_type', ['buyer', 'seller', 'admin'])->default('buyer');
    $table->boolean('is_verified')->default(false);
    $table->timestamp('last_login_at')->nullable();
});

-- Listings table
Schema::create('listings', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->string('title', 200);
    $table->foreignId('brand_id')->nullable()->constrained();
    $table->string('model', 100)->nullable();
    $table->year('year')->nullable();
    $table->enum('condition', ['new', 'used']);
    $table->decimal('price', 12, 2)->nullable();
    $table->string('currency', 3)->default('USD');
    $table->string('location', 100);
    $table->text('description');
    $table->json('specifications')->nullable(); // Stores all spec fields
    $table->json('specifications_metric')->nullable(); // Metric conversions
    $table->string('stock_number', 50)->unique();
    $table->foreignId('industry_id')->constrained();
    $table->foreignId('category_id')->constrained();
    $table->foreignId('equipment_type_id')->nullable()->constrained();
    $table->foreignId('group_id')->nullable()->constrained();
    $table->enum('status', ['For Sale', 'Sold', 'Pending'])->default('For Sale');
    $table->string('control_type')->nullable(); // e.g., "Fanuc 16T / MSC516 Control"
    $table->text('additional_features')->nullable(); // e.g., "BB500/230 Air Chucks, Chip Conveyor"
    $table->unsignedInteger('views')->default(0);
    $table->boolean('featured')->default(false);
    $table->timestamps();
    
    // Indexes for performance
    $table->index(['status', 'created_at']);
    $table->index(['industry_id', 'category_id', 'equipment_type_id']);
    $table->index(['brand_id', 'model']);
    $table->fullText(['title', 'description', 'additional_features']);
});

-- Listing images table
Schema::create('listing_images', function (Blueprint $table) {
    $table->id();
    $table->foreignId('listing_id')->constrained()->onDelete('cascade');
    $table->string('path');
    $table->string('thumbnail_path')->nullable();
    $table->unsignedInteger('sort_order')->default(0);
    $table->boolean('is_primary')->default(false);
    $table->timestamps();
    
    $table->index(['listing_id', 'sort_order']);
});

-- Quote requests table
Schema::create('quote_requests', function (Blueprint $table) {
    $table->id();
    $table->foreignId('listing_id')->constrained()->onDelete('cascade');
    $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Buyer
    $table->string('name', 100);
    $table->string('email', 100);
    $table->string('phone', 20)->nullable();
    $table->string('company', 100)->nullable();
    $table->text('message');
    $table->enum('status', ['pending', 'responded', 'closed'])->default('pending');
    $table->timestamp('responded_at')->nullable();
    $table->timestamps();
    
    $table->index(['listing_id', 'created_at']);
    $table->index(['user_id', 'created_at']);
});

-- Saved searches table
Schema::create('saved_searches', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->string('name', 100);
    $table->json('criteria'); // Stores search parameters
    $table->boolean('email_alerts')->default(false);
    $table->timestamp('last_alerted_at')->nullable();
    $table->timestamps();
});

-- Favorites table
Schema::create('favorites', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->foreignId('listing_id')->constrained()->onDelete('cascade');
    $table->timestamps();
    
    $table->unique(['user_id', 'listing_id']);
});
```

## Laravel Configuration

### Environment Configuration (.env)
```env
APP_NAME="Sector360"
APP_ENV=local
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sector360
DB_USERNAME=root
DB_PASSWORD=

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

MEDIA_DISK=public
```

### Tailwind Configuration (tailwind.config.js)
```javascript
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],
    theme: {
        extend: {
            colors: {
                'sector': {
                    'dark': '#0a0a0a',          // Main background
                    'darker': '#050505',        // Darker sections
                    'card': '#141414',          // Card backgrounds
                    'border': '#282828',        // Borders
                    'primary': '#3b82f6',       // Primary blue
                    'primary-hover': '#2563eb', // Primary hover
                    'text': '#e5e5e5',          // Main text
                    'text-muted': '#a3a3a3',    // Muted text
                }
            },
            fontFamily: {
                sans: ['Inter', 'sans-serif'],
            },
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
        require('@tailwindcss/aspect-ratio'),
    ],
}
```

### App Layout Configuration
```php
// config/app.php
return [
    'name' => env('APP_NAME', 'Sector360'),
    'timezone' => 'UTC',
    'locale' => 'en',
    
    // Custom configuration
    'listings' => [
        'per_page' => 24,
        'max_images' => 10,
        'image_quality' => 85,
        'thumbnail_width' => 300,
        'thumbnail_height' => 300,
    ],
    
    'industries' => [
        'metalworking' => 'Metalworking',
        'processing-packaging' => 'Processing & Packaging',
        'medical-laboratory' => 'Medical & Laboratory',
        'material-handling' => 'Material Handling',
        'electrical-power' => 'Electrical & Power',
        'robotics-automation' => 'Robotics & Automation',
        'woodworking' => 'Woodworking',
        'construction' => 'Construction',
        'agriculture' => 'Agriculture',
        'printing-paper' => 'Printing & Paper',
        'trucks-trailers' => 'Trucks/Trailers',
        'commercial-equipment' => 'Commercial Equipment & Supplies',
    ],
];
```
