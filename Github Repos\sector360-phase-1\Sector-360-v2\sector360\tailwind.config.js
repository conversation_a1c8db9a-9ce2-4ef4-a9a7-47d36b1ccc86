import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import aspectRatio from '@tailwindcss/aspect-ratio';

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: 'class',
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js',
    ],
    theme: {
        extend: {
            colors: {
                'sector': {
                    'bg': '#0f0f0f',          // Main background
                    'card': '#1a1a1a',       // Card backgrounds
                    'border': '#2a2a2a',     // Borders
                    'primary': '#2563eb',    // Primary blue
                    'primary-hover': '#1d4ed8', // Primary hover
                    'accent': '#10b981',     // Accent green
                    'danger': '#ef4444',     // Danger red
                    'warning': '#f59e0b',    // Warning yellow
                    'text': '#e5e5e5',       // Main text
                    'text-muted': '#a3a3a3', // Muted text
                }
            },
            fontFamily: {
                'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
            },
        },
    },
    plugins: [
        forms,
        typography,
        aspectRatio,
    ],
};
