# Sector360 Marketplace Demo - Part 6: Data & Deployment

## Database Seeders

### IndustrySeeder

```php
<?php
// database/seeders/IndustrySeeder.php
namespace Database\Seeders;

use App\Models\Industry;
use App\Models\Group;
use App\Models\Category;
use App\Models\EquipmentType;
use Illuminate\Database\Seeder;

class IndustrySeeder extends Seeder
{
    public function run()
    {
        $industries = [
            ['name' => 'Metalworking', 'icon_class' => 'fas fa-cogs', 'groups' => [
                'Lathes' => ['CNC Lathes', 'Manual Lathes', 'Vertical Lathes'],
                'Mills' => ['CNC Mills', 'Manual Mills', 'Vertical Machining Centers'],
                'Grinders' => ['Surface Grinders', 'Cylindrical Grinders', 'Tool Grinders'],
            ]],
            ['name' => 'Processing & Packaging', 'icon_class' => 'fas fa-box', 'groups' => [
                'Packaging Equipment' => ['Form Fill Seal', 'Case Packers', 'Shrink Wrappers'],
                'Processing Lines' => ['Conveyors', 'Sorting Systems', 'Inspection Systems'],
            ]],
            ['name' => 'Medical & Laboratory', 'icon_class' => 'fas fa-microscope', 'groups' => [
                'Lab Equipment' => ['Centrifuges', 'Microscopes', 'Analyzers'],
                'Medical Devices' => ['Imaging Equipment', 'Surgical Equipment', 'Patient Monitors'],
            ]],
            ['name' => 'Material Handling', 'icon_class' => 'fas fa-warehouse', 'groups' => [
                'Forklifts' => ['Electric Forklifts', 'Diesel Forklifts', 'Reach Trucks'],
                'Storage' => ['Pallet Racking', 'Shelving Systems', 'Mezzanines'],
            ]],
            ['name' => 'Electrical & Power', 'icon_class' => 'fas fa-bolt', 'groups' => [
                'Generators' => ['Diesel Generators', 'Natural Gas Generators', 'Portable Generators'],
                'Transformers' => ['Power Transformers', 'Distribution Transformers'],
            ]],
            ['name' => 'Robotics & Automation', 'icon_class' => 'fas fa-robot', 'groups' => [
                'Industrial Robots' => ['Welding Robots', 'Assembly Robots', 'Palletizing Robots'],
                'Automation Systems' => ['PLCs', 'HMIs', 'Vision Systems'],
            ]],
            ['name' => 'Woodworking', 'icon_class' => 'fas fa-tree', 'groups' => [
                'Saws' => ['Table Saws', 'Band Saws', 'Panel Saws'],
                'Planers & Jointers' => ['Thickness Planers', 'Jointers', 'Combination Machines'],
            ]],
            ['name' => 'Construction', 'icon_class' => 'fas fa-hard-hat', 'groups' => [
                'Excavators' => ['Mini Excavators', 'Crawler Excavators', 'Wheeled Excavators'],
                'Loaders' => ['Skid Steers', 'Wheel Loaders', 'Backhoes'],
            ]],
            ['name' => 'Agriculture', 'icon_class' => 'fas fa-tractor', 'groups' => [
                'Tractors' => ['Compact Tractors', 'Utility Tractors', 'Row Crop Tractors'],
                'Harvesting' => ['Combines', 'Forage Harvesters', 'Balers'],
            ]],
            ['name' => 'Printing & Paper', 'icon_class' => 'fas fa-print', 'groups' => [
                'Printing Presses' => ['Offset Presses', 'Digital Presses', 'Flexo Presses'],
                'Finishing' => ['Cutters', 'Folders', 'Binders'],
            ]],
            ['name' => 'Trucks/Trailers', 'icon_class' => 'fas fa-truck', 'groups' => [
                'Trucks' => ['Box Trucks', 'Dump Trucks', 'Flatbed Trucks'],
                'Trailers' => ['Dry Van Trailers', 'Refrigerated Trailers', 'Flatbed Trailers'],
            ]],
            ['name' => 'Commercial Equipment & Supplies', 'icon_class' => 'fas fa-dolly', 'groups' => [
                'Restaurant Equipment' => ['Ovens', 'Refrigerators', 'Dishwashers'],
                'Office Equipment' => ['Copiers', 'Printers', 'Shredders'],
            ]],
        ];

        foreach ($industries as $index => $industryData) {
            $industry = Industry::create([
                'name' => $industryData['name'],
                'slug' => \Str::slug($industryData['name']),
                'description' => "Quality {$industryData['name']} equipment and machinery",
                'icon_class' => $industryData['icon_class'],
                'sort_order' => $index + 1,
                'is_active' => true,
            ]);

            foreach ($industryData['groups'] as $groupName => $categories) {
                $group = Group::create([
                    'industry_id' => $industry->id,
                    'name' => $groupName,
                    'slug' => \Str::slug($groupName),
                    'description' => "{$groupName} equipment and machinery",
                    'sort_order' => 1,
                    'is_active' => true,
                ]);

                foreach ($categories as $catIndex => $categoryName) {
                    $category = Category::create([
                        'group_id' => $group->id,
                        'name' => $categoryName,
                        'slug' => \Str::slug($categoryName),
                        'description' => "{$categoryName} equipment",
                        'sort_order' => $catIndex + 1,
                        'is_active' => true,
                    ]);

                    // Create equipment type (same as category for simplicity)
                    EquipmentType::create([
                        'category_id' => $category->id,
                        'name' => $categoryName,
                        'slug' => \Str::slug($categoryName),
                        'sort_order' => 1,
                        'is_active' => true,
                    ]);
                }
            }
        }
    }
}
```

### BrandSeeder

```php
<?php
// database/seeders/BrandSeeder.php
namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    public function run()
    {
        $brands = [
            'Haas', 'Mazak', 'DMG Mori', 'Okuma', 'Doosan', 'Fanuc',
            'John Deere', 'Caterpillar', 'Komatsu', 'Volvo', 'JCB',
            'Lincoln Electric', 'Miller', 'ESAB', 'Hypertherm',
            'Siemens', 'ABB', 'Schneider Electric', 'Rockwell Automation',
            'Heidelberg', 'Komori', 'KBA', 'Xerox', 'Canon',
            'Freightliner', 'Peterbilt', 'Kenworth', 'Mack', 'International',
            'Toyota', 'Hyster', 'Crown', 'Raymond', 'Yale',
            'Amada', 'Trumpf', 'Bystronic', 'Cincinnati', 'Accurpress',
            'Powermatic', 'Grizzly', 'SawStop', 'Festool', 'DeWalt',
            'Thermo Fisher', 'Agilent', 'Waters', 'PerkinElmer', 'Bruker',
        ];

        foreach ($brands as $brandName) {
            Brand::create([
                'name' => $brandName,
                'slug' => \Str::slug($brandName),
                'description' => "{$brandName} equipment and machinery",
                'website' => 'https://www.' . \Str::slug($brandName) . '.com',
                'is_active' => true,
                'is_featured' => rand(0, 10) > 7, // 30% chance of being featured
            ]);
        }
    }
}
```

## Factory Definitions

### UserFactory

```php
<?php
// database/factories/UserFactory.php
namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    public function definition()
    {
        $userType = $this->faker->randomElement(['buyer', 'seller']);
        
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
            'remember_token' => Str::random(10),
            'user_type' => $userType,
            'company_name' => $userType === 'seller' ? $this->faker->company() : null,
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state_province' => $this->faker->state(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->country(),
        ];
    }

    public function seller()
    {
        return $this->state(function (array $attributes) {
            return [
                'user_type' => 'seller',
                'company_name' => $this->faker->company(),
            ];
        });
    }
}
```

### ListingFactory

```php
<?php
// database/factories/ListingFactory.php
namespace Database\Factories;

use App\Models\Listing;
use App\Models\User;
use App\Models\Industry;
use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;

class ListingFactory extends Factory
{
    public function definition()
    {
        $price = $this->faker->randomFloat(2, 5000, 500000);
        $industry = Industry::inRandomOrder()->first();
        $group = $industry->groups()->inRandomOrder()->first();
        $category = $group->categories()->inRandomOrder()->first();
        $equipmentType = $category->equipmentTypes()->inRandomOrder()->first();
        
        return [
            'user_id' => User::where('user_type', 'seller')->inRandomOrder()->first()->id,
            'title' => $this->faker->words(4, true) . ' ' . $equipmentType->name,
            'slug' => Str::slug($this->faker->unique()->words(4, true)),
            'brand_id' => Brand::inRandomOrder()->first()->id,
            'model' => strtoupper($this->faker->bothify('??-####')),
            'year' => $this->faker->numberBetween(2000, 2024),
            'condition' => $this->faker->randomElement(['new', 'used']),
            'price' => $price,
            'currency' => 'USD',
            'negotiable' => $this->faker->boolean(30),
            'location' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state_province' => $this->faker->state(),
            'country' => 'United States',
            'description' => $this->faker->paragraphs(3, true),
            'specifications' => [
                'Power' => $this->faker->randomElement(['220V', '440V', '3-Phase']),
                'Weight' => $this->faker->numberBetween(100, 10000) . ' lbs',
                'Dimensions' => $this->faker->numberBetween(10, 100) . '" x ' . 
                               $this->faker->numberBetween(10, 100) . '" x ' . 
                               $this->faker->numberBetween(10, 100) . '"',
            ],
            'stock_number' => strtoupper($this->faker->bothify('STK-####-??')),
            'industry_id' => $industry->id,
            'group_id' => $group->id,
            'category_id' => $category->id,
            'equipment_type_id' => $equipmentType->id,
            'views' => $this->faker->numberBetween(0, 1000),
            'status' => 'active',
            'is_featured' => $this->faker->boolean(10),
        ];
    }
}
```

## Database Seeding Command

```php
<?php
// database/seeders/DatabaseSeeder.php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Listing;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'user_type' => 'admin',
        ])->assignRole('admin');

        // Seed industries and brands
        $this->call([
            IndustrySeeder::class,
            BrandSeeder::class,
        ]);

        // Create demo users
        User::factory(5)->create(['user_type' => 'buyer'])->each(function ($user) {
            $user->assignRole('buyer');
        });

        User::factory(20)->seller()->create()->each(function ($user) {
            $user->assignRole('seller');
        });

        // Create listings
        Listing::factory(500)->create()->each(function ($listing) {
            // Create 1-5 images per listing
            $imageCount = rand(1, 5);
            for ($i = 0; $i < $imageCount; $i++) {
                $listing->images()->create([
                    'path' => "/images/sample/equipment-{$i}.jpg",
                    'thumbnail_path' => "/images/sample/equipment-{$i}-thumb.jpg",
                    'sort_order' => $i,
                    'is_primary' => $i === 0,
                ]);
            }
        });
    }
}
```

## Deployment Instructions

### Initial Setup

```bash
# 1. Clone repository and install dependencies
composer install
npm install

# 2. Environment setup
cp .env.example .env
php artisan key:generate

# 3. Configure database in .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sector360
DB_USERNAME=your_username
DB_PASSWORD=your_password

# 4. Run migrations and seed database
php artisan migrate --seed

# 5. Create storage link
php artisan storage:link

# 6. Build assets
npm run build

# 7. Start development server
php artisan serve
```

### Production Deployment

```bash
# 1. Install production dependencies
composer install --no-dev --optimize-autoloader
npm install --production

# 2. Build production assets
npm run build

# 3. Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 4. Set proper permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### Sample Nginx Configuration

```nginx
server {
    listen 80;
    server_name sector360.com www.sector360.com;
    root /var/www/sector360/public;

    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```
