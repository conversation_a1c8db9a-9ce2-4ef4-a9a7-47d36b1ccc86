<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_requests', function (Blueprint $table) {
            // Add seller_id column
            $table->foreignId('seller_id')->after('user_id')->constrained('users')->onDelete('cascade');

            // Remove columns we don't need anymore
            $table->dropColumn(['name', 'email', 'phone', 'company']);

            // Add response column for seller responses
            $table->text('response')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_requests', function (Blueprint $table) {
            // Add back the old columns
            $table->string('name', 100)->after('user_id');
            $table->string('email', 100)->after('name');
            $table->string('phone', 20)->nullable()->after('email');
            $table->string('company', 100)->nullable()->after('phone');

            // Remove the new columns
            $table->dropForeign(['seller_id']);
            $table->dropColumn(['seller_id', 'response']);
        });
    }
};
