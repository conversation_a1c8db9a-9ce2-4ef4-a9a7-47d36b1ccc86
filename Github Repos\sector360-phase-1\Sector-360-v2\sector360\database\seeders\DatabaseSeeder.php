<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            IndustrySeeder::class,
            BrandSeeder::class,
            CategorySeeder::class,
            ListingSeeder::class,
        ]);

        // Create a demo admin user
        \App\Models\User::factory()->create([
            'name' => 'Demo Admin',
            'email' => '<EMAIL>',
            'user_type' => 'admin',
            'is_verified' => true,
            'company_name' => 'Sector360 Demo',
        ]);

        // Create a demo seller
        \App\Models\User::factory()->create([
            'name' => 'Demo Seller',
            'email' => '<EMAIL>',
            'user_type' => 'seller',
            'is_verified' => true,
            'company_name' => 'Industrial Equipment Co.',
            'location' => 'Chicago, IL',
        ]);

        // Create a demo buyer
        \App\Models\User::factory()->create([
            'name' => 'Demo Buyer',
            'email' => '<EMAIL>',
            'user_type' => 'buyer',
            'is_verified' => true,
            'company_name' => 'Manufacturing Solutions Inc.',
            'location' => 'Detroit, MI',
        ]);
    }
}
