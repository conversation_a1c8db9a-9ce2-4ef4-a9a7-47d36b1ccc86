# Sector360 Marketplace Demo - AI Prompt Index

This comprehensive prompt is split into modular files for better AI processing. Each file is under 500 lines to ensure optimal performance with AI coding assistants.

## Prompt Structure

1. **[prompt1.md](./prompt1.md)** - Foundation & Database
   - Project Overview
   - Technical Stack & Dependencies  
   - Database Schema

2. **[prompt2.md](./prompt2.md)** - Core Models
   - User Model
   - Listing Model
   - Industry Model

3. **[prompt2b.md](./prompt2b.md)** - Supporting Models
   - Group, Category, EquipmentType Models
   - Brand, ListingImage Models
   - QuoteRequest, SavedSearch Models

4. **[prompt3.md](./prompt3.md)** - UI/UX Design System & Components
   - Tailwind Configuration
   - Layout Templates
   - UI Components

5. **[prompt4.md](./prompt4.md)** - Page Templates
   - Homepage
   - Search Results Page
   - Listing Detail Page
   - Add Equipment Page

6. **[prompt5.md](./prompt5.md)** - Controllers & Routes
   - Controller Implementations
   - Route Definitions
   - Business Logic

7. **[prompt6.md](./prompt6.md)** - Data & Deployment
   - Database Seeders
   - Factory Definitions
   - Sample Data
   - Deployment Instructions

## Usage Instructions

1. **For AI Code Generation**: Feed the files to your AI coding assistant in order (prompt1.md through prompt6.md)

2. **For Development**: 
   - Start with prompt1.md to understand the project structure
   - Use prompt2.md and prompt2b.md for model relationships
   - Reference prompt3.md for UI consistency
   - Follow prompt4.md for page implementations
   - Implement logic using prompt5.md patterns
   - Use prompt6.md for seeding and deployment

3. **Key Notes**:
   - Each file builds upon the previous ones
   - Maintain consistency with the defined patterns
   - The dark theme and industrial marketplace focus should be preserved throughout
   - All files are kept under 300 lines for optimal AI processing

## Key Project Details

- **Project Name**: Sector360 - "The World's Business Marketplace"
- **Framework**: Laravel PHP with Blade templates
- **Styling**: Tailwind CSS with dark theme
- **Database**: MySQL with Eloquent ORM
- **Authentication**: Laravel Breeze (email/password only)
- **Key Feature**: Request quote system (no payment processing)
